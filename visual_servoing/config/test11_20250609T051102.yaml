# Mission parameters for test11
# Generated on 2025-06-09T05:11:02.592Z

# --- FIELD PARAMETERS ---
row_length: 50          # (meters) The expected length of each crop row.
row_spacing: 0.75        # (meters) The distance between adjacent crop rows.
field_direction: "right"   # ("right" or "left") The direction the robot should turn at the end of the row.

# --- ROW TRANSITION MANEUVER PARAMETERS ---
A: 1.0                   # (meters) Distance to move forward when exiting/entering a row.
theta_degrees: 45        # (degrees) Rotation angle (theta) for the turning maneuver.
linear_speed: 1          # (m/s) Linear speed during the turning maneuver.
angular_speed: 2.0       # (rad/s) Angular speed during the turning maneuver.

# --- DETECTION PARAMETERS ---
detection_sensitivity: 0.7  # Weed detection sensitivity (0.1 = low, 1.0 = high)
laser_power: 80            # Laser power percentage (10-100%)

# --- DISTANCE TRACKING PARAMETERS ---
end_of_row_threshold: 0.5    # (meters) Distance threshold to detect end of row.
wrong_distance_threshold: 2.0 # (meters) Distance threshold to detect wrong direction.
use_odometry: true           # Use odometry for distance tracking.
use_feedback: true           # Use feedback control.

# --- NEIGHBOURHOOD TRACKER PARAMETERS ---
neighbourhood_width: 150     # (pixels) Width of the tracking window.
neighbourhood_height: 180    # (pixels) Height of the tracking window.
initial_x: 320               # (pixels) Initial X coordinate of the tracking window center.
initial_y: 240               # (pixels) Initial Y coordinate of the tracking window center.

# --- HSV COLOR FILTER PARAMETERS (for green crop rows) ---
h_min: 35                  # (degrees) Minimum Hue value.
h_max: 85                  # (degrees) Maximum Hue value.
s_min: 50                  # (0-255) Minimum Saturation value.
s_max: 255                 # (0-255) Maximum Saturation value.
v_min: 50                  # (0-255) Minimum Value (Brightness) value.
v_max: 255                 # (0-255) Maximum Value (Brightness) value.

# --- LINE FOLLOWING PARAMETERS ---
point_spacing: 70          # (pixels)  Spacing used when sampling points from the detected crop row.
kp: 0.005                # Proportional gain for steering control.
