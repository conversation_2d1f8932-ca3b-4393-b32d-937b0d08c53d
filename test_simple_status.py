#!/usr/bin/env python3

"""
Simple test script for the unified status topic system.
"""

import rospy
import json
from std_msgs.msg import String

def status_callback(msg):
    """Callback for status messages"""
    try:
        status_data = json.loads(msg.data)
        print("=" * 50)
        print("📊 Status Update Received:")
        print(f"  Timestamp: {status_data.get('timestamp', 'Unknown')}")
        print(f"  Success: {'✅' if status_data.get('success', False) else '❌'}")
        print(f"  Message: {status_data.get('message', 'No message')}")
        
        if 'node' in status_data:
            print(f"  Node: {status_data['node']}")
        
        if 'config_file' in status_data:
            print(f"  Config File: {status_data['config_file']}")
        
        if 'error' in status_data and status_data['error']:
            print(f"  Error: {status_data['error']}")
        
        if 'services' in status_data:
            print("  Services:")
            for service_name, service_info in status_data['services'].items():
                status_icon = '✅' if service_info.get('available', False) else '❌'
                print(f"    {service_name}: {status_icon} {service_info.get('message', 'No info')}")
        
        print("=" * 50)
        
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing status JSON: {e}")
        print(f"Raw message: {msg.data}")
    except Exception as e:
        print(f"❌ Error processing status: {e}")

def main():
    """Main function"""
    rospy.init_node('status_monitor', anonymous=True)
    
    print("🔍 Monitoring unified status topic: /system/reload_status")
    print("Press Ctrl+C to stop...")
    print()
    
    # Subscribe to the unified status topic
    rospy.Subscriber('/system/reload_status', String, status_callback)
    
    try:
        rospy.spin()
    except KeyboardInterrupt:
        print("\n👋 Status monitoring stopped by user")

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        pass
