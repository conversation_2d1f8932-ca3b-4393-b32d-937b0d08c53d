import React, { useState, useContext, useEffect, useRef } from 'react';
import { RosContext } from './RosConnection';
import './CameraFeed.css';

const CameraFeed = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [imageData] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState(null);
  const [imageTopic, setImageTopic] = useState('/camera_sim/color/image_raw');
  const canvasRef = useRef(null);
  const imageSubscriberRef = useRef(null);

  // Available camera topics
  const cameraTopics = [
    '/camera_sim/color/image_raw',
    '/camera/rgb/image_rect_color',
    '/camera/color/image_raw',
    '/kinect2/qhd/image_color_rect'
  ];

  useEffect(() => {
    const initializeStream = () => {
      if (!isConnected || !ros || !window.ROSLIB) {
        setIsStreaming(false);
        setError('Not connected to ROS or ROSLIB not loaded');
        return;
      }

      startImageStream();
    };

    initializeStream();

    return () => {
      stopImageStream();
    };
  }, [ros, isConnected, imageTopic]); // eslint-disable-line react-hooks/exhaustive-deps

  const startImageStream = () => {
    if (!ros || !isConnected || !window.ROSLIB) return;

    try {
      // Clean up existing subscription
      stopImageStream();

      // For now, show a placeholder message about camera streaming
      setError('Camera streaming requires web_video_server. Please install it with: sudo apt-get install ros-noetic-web-video-server');
      setIsStreaming(false);

      // TODO: Implement proper image streaming once web_video_server is available
      // This is a placeholder for future implementation
      console.log(`Would subscribe to camera topic: ${imageTopic}`);

    } catch (err) {
      console.error('Error starting image stream:', err);
      setError('Failed to start camera stream');
      setIsStreaming(false);
    }
  };

  const stopImageStream = () => {
    if (imageSubscriberRef.current) {
      imageSubscriberRef.current.unsubscribe();
      imageSubscriberRef.current = null;
    }
    setIsStreaming(false);
  };

  const handleTopicChange = (newTopic) => {
    setImageTopic(newTopic);
  };

  const getStatusColor = () => {
    if (!isConnected) return '#f44336';
    if (isStreaming) return '#4CAF50';
    return '#ff9800';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Disconnected';
    if (isStreaming) return 'Streaming';
    if (error) return 'Error';
    return 'Connecting...';
  };

  return (
    <div className="camera-feed">
      <div className="camera-header">
        <h2>Camera Feed</h2>
        <div className="camera-controls">
          <select 
            value={imageTopic} 
            onChange={(e) => handleTopicChange(e.target.value)}
            className="topic-selector"
            disabled={!isConnected}
          >
            {cameraTopics.map(topic => (
              <option key={topic} value={topic}>{topic}</option>
            ))}
          </select>
          <div className="status-indicator">
            <div 
              className="status-dot" 
              style={{ backgroundColor: getStatusColor() }}
            ></div>
            <span className="status-text">{getStatusText()}</span>
          </div>
        </div>
      </div>

      <div className="camera-container">
        {error && (
          <div className="error-message">
            <p>⚠️ {error}</p>
            <button onClick={startImageStream} disabled={!isConnected}>
              Retry
            </button>
          </div>
        )}
        
        <canvas 
          ref={canvasRef}
          className="camera-canvas"
          style={{ display: error ? 'none' : 'block' }}
        />
        
        {!isStreaming && !error && isConnected && (
          <div className="loading-message">
            <p>📹 Connecting to camera...</p>
          </div>
        )}
        
        {!isConnected && (
          <div className="disconnected-message">
            <p>🔌 Connect to ROS to view camera feed</p>
          </div>
        )}
      </div>

      {imageData && (
        <div className="image-info">
          <span>Resolution: {imageData.width}x{imageData.height}</span>
          <span>Encoding: {imageData.encoding}</span>
          <span>Topic: {imageTopic}</span>
        </div>
      )}
    </div>
  );
};

export default CameraFeed;
