[{"directory": "/home/<USER>/weednix_ws/src/build/gtest/googlemock", "command": "/usr/bin/c++  -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_main_EXPORTS -isystem /usr/src/googletest/googlemock/include -isystem /usr/src/googletest/googlemock -isystem /usr/src/googletest/googletest/include -isystem /usr/src/googletest/googletest  -g -fPIC   -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -DGTEST_HAS_PTHREAD=1 -std=c++11 -o CMakeFiles/gmock_main.dir/src/gmock_main.cc.o -c /usr/src/googletest/googlemock/src/gmock_main.cc", "file": "/usr/src/googletest/googlemock/src/gmock_main.cc"}, {"directory": "/home/<USER>/weednix_ws/src/build/gtest/googlemock", "command": "/usr/bin/c++  -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgmock_EXPORTS -I/usr/src/googletest/googlemock/include -I/usr/src/googletest/googlemock -isystem /usr/src/googletest/googletest/include -isystem /usr/src/googletest/googletest  -g -fPIC   -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -DGTEST_HAS_PTHREAD=1 -std=c++11 -o CMakeFiles/gmock.dir/src/gmock-all.cc.o -c /usr/src/googletest/googlemock/src/gmock-all.cc", "file": "/usr/src/googletest/googlemock/src/gmock-all.cc"}, {"directory": "/home/<USER>/weednix_ws/src/build/gtest/googletest", "command": "/usr/bin/c++  -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_main_EXPORTS -isystem /usr/src/googletest/googletest/include -isystem /usr/src/googletest/googletest  -g -fPIC   -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -DGTEST_HAS_PTHREAD=1 -std=c++11 -o CMakeFiles/gtest_main.dir/src/gtest_main.cc.o -c /usr/src/googletest/googletest/src/gtest_main.cc", "file": "/usr/src/googletest/googletest/src/gtest_main.cc"}, {"directory": "/home/<USER>/weednix_ws/src/build/gtest/googletest", "command": "/usr/bin/c++  -DGTEST_CREATE_SHARED_LIBRARY=1 -Dgtest_EXPORTS -I/usr/src/googletest/googletest/include -I/usr/src/googletest/googletest  -g -fPIC   -Wall -Wshadow -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -std=c++11 -o CMakeFiles/gtest.dir/src/gtest-all.cc.o -c /usr/src/googletest/googletest/src/gtest-all.cc", "file": "/usr/src/googletest/googletest/src/gtest-all.cc"}, {"directory": "/home/<USER>/weednix_ws/src/build/realsense_gazebo_plugin", "command": "/usr/bin/c++  -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_DATE_TIME_DYN_LINK -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_IOSTREAMS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_REGEX_DYN_LINK -DBOOST_SYSTEM_DYN_LINK -DBOOST_TEST_DYN_LINK -DBOOST_THREAD_DYN_LINK -DLIBBULLET_VERSION=2.88 -DLIBBULLET_VERSION_GT_282 -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"realsense_gazebo_plugin\\\" -Drealsense_gazebo_plugin_EXPORTS -I/home/<USER>/weednix_ws/src/realsense_gazebo_plugin/include -isystem /opt/ros/noetic/include -isystem /opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/gazebo-11 -isystem /usr/include/bullet -isystem /usr/include/simbody -isystem /usr/include/sdformat-9.10 -isystem /usr/include/ignition/math6 -isystem /usr/include/OGRE -isystem /usr/include/OGRE/Terrain -isystem /usr/include/OGRE/Paging -isystem /usr/include/ignition/transport8 -isystem /usr/include/ignition/msgs5 -isystem /usr/include/ignition/common3 -isystem /usr/include/ignition/fuel_tools4 -isystem /usr/include/eigen3 -isystem /usr/include/sdformat-9.10/sdf/.. -isystem /usr/include/ignition/cmake2 -isystem /usr/include/uuid    -g -fPIC   -I/usr/include/uuid -std=gnu++17 -o CMakeFiles/realsense_gazebo_plugin.dir/src/RealSensePlugin.cpp.o -c /home/<USER>/weednix_ws/src/realsense_gazebo_plugin/src/RealSensePlugin.cpp", "file": "/home/<USER>/weednix_ws/src/realsense_gazebo_plugin/src/RealSensePlugin.cpp"}, {"directory": "/home/<USER>/weednix_ws/src/build/realsense_gazebo_plugin", "command": "/usr/bin/c++  -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_DATE_TIME_DYN_LINK -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_IOSTREAMS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_REGEX_DYN_LINK -DBOOST_SYSTEM_DYN_LINK -DBOOST_TEST_DYN_LINK -DBOOST_THREAD_DYN_LINK -DLIBBULLET_VERSION=2.88 -DLIBBULLET_VERSION_GT_282 -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"realsense_gazebo_plugin\\\" -Drealsense_gazebo_plugin_EXPORTS -I/home/<USER>/weednix_ws/src/realsense_gazebo_plugin/include -isystem /opt/ros/noetic/include -isystem /opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/gazebo-11 -isystem /usr/include/bullet -isystem /usr/include/simbody -isystem /usr/include/sdformat-9.10 -isystem /usr/include/ignition/math6 -isystem /usr/include/OGRE -isystem /usr/include/OGRE/Terrain -isystem /usr/include/OGRE/Paging -isystem /usr/include/ignition/transport8 -isystem /usr/include/ignition/msgs5 -isystem /usr/include/ignition/common3 -isystem /usr/include/ignition/fuel_tools4 -isystem /usr/include/eigen3 -isystem /usr/include/sdformat-9.10/sdf/.. -isystem /usr/include/ignition/cmake2 -isystem /usr/include/uuid    -g -fPIC   -I/usr/include/uuid -std=gnu++17 -o CMakeFiles/realsense_gazebo_plugin.dir/src/gazebo_ros_realsense.cpp.o -c /home/<USER>/weednix_ws/src/realsense_gazebo_plugin/src/gazebo_ros_realsense.cpp", "file": "/home/<USER>/weednix_ws/src/realsense_gazebo_plugin/src/gazebo_ros_realsense.cpp"}, {"directory": "/home/<USER>/weednix_ws/src/build/visual-crop-row", "command": "/usr/bin/c++  -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"visual_crop_row\\\" -I/home/<USER>/weednix_ws/src/build/devel/include -I/home/<USER>/weednix_ws/src/visual-crop-row/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/opencv4 -isystem /usr/include/eigen3  -Wall -Wextra -fPIC -g   -std=gnu++11 -o CMakeFiles/agribot_vs_node.dir/src/agribot_vs_node.cpp.o -c /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs_node.cpp", "file": "/home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs_node.cpp"}, {"directory": "/home/<USER>/weednix_ws/src/build/visual-crop-row", "command": "/usr/bin/c++  -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"visual_crop_row\\\" -I/home/<USER>/weednix_ws/src/build/devel/include -I/home/<USER>/weednix_ws/src/visual-crop-row/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -isystem /usr/include/opencv4 -isystem /usr/include/eigen3  -Wall -Wextra -fPIC -g   -std=gnu++11 -o CMakeFiles/agribot_vs_node.dir/src/agribot_vs_nodehandler.cpp.o -c /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs_nodehandler.cpp", "file": "/home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs_nodehandler.cpp"}, {"directory": "/home/<USER>/weednix_ws/src/build/visual-crop-row", "command": "/usr/bin/c++  -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"visual_crop_row\\\" -Dvisual_crop_row_core_EXPORTS -I/home/<USER>/weednix_ws/src/build/devel/include -I/home/<USER>/weednix_ws/src/visual-crop-row/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/include/opencv4 -I/usr/include/eigen3  -Wall -Wextra -fPIC -g -fPIC   -std=gnu++11 -o CMakeFiles/visual_crop_row_core.dir/src/agribot_vs.cpp.o -c /home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs.cpp", "file": "/home/<USER>/weednix_ws/src/visual-crop-row/src/agribot_vs.cpp"}, {"directory": "/home/<USER>/weednix_ws/src/build/visual_servoing", "command": "/usr/bin/c++  -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\\\"visual_servoing\\\" -I/home/<USER>/weednix_ws/src/build/devel/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/include/opencv4  -g   -o CMakeFiles/path_publisher.dir/src/path_publisher.cpp.o -c /home/<USER>/weednix_ws/src/visual_servoing/src/path_publisher.cpp", "file": "/home/<USER>/weednix_ws/src/visual_servoing/src/path_publisher.cpp"}]