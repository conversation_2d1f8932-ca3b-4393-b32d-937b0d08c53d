#!/usr/bin/env python3

"""
Simple system status publisher that monitors parameter reload status
and publishes to a single topic for web interface consumption.
"""

import rospy
import json
from std_msgs.msg import String
from std_srvs.srv import Trigger

class SystemStatusPublisher:
    def __init__(self):
        rospy.init_node('system_status_publisher', anonymous=True)
        
        # Publisher for unified status
        self.status_pub = rospy.Publisher('/system/reload_status', String, queue_size=1)
        
        # Timer for periodic status publishing
        self.timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("System status publisher started")
        
        # Publish initial status
        self.publish_status()

    def check_service_status(self, service_name, service_type=Trigger):
        """Check if a service is available and working"""
        try:
            rospy.wait_for_service(service_name, timeout=1.0)
            service_proxy = rospy.ServiceProxy(service_name, service_type)
            # Don't actually call the service, just check if it's available
            return True, "Service available"
        except rospy.ROSException as e:
            return False, f"Service unavailable: {str(e)}"
        except Exception as e:
            return False, f"Error: {str(e)}"

    def publish_status(self, event=None):
        """Publish current system status"""
        try:
            status_data = {
                "timestamp": rospy.Time.now().to_sec(),
                "success": True,
                "message": "System operational",
                "services": {}
            }
            
            # Check geofencing reload service
            geofencing_available, geofencing_msg = self.check_service_status('/geofencing_node/reload_boundary')
            status_data["services"]["geofencing"] = {
                "available": geofencing_available,
                "message": geofencing_msg
            }
            
            # Check row crop follower reload service
            row_crop_available, row_crop_msg = self.check_service_status('/row_crop_follower/reload_parameters')
            status_data["services"]["row_crop_follower"] = {
                "available": row_crop_available,
                "message": row_crop_msg
            }
            
            # Overall system status
            overall_success = geofencing_available and row_crop_available
            status_data["success"] = overall_success
            
            if overall_success:
                status_data["message"] = "All parameter reload services operational"
            else:
                failed_services = []
                if not geofencing_available:
                    failed_services.append("geofencing")
                if not row_crop_available:
                    failed_services.append("row_crop_follower")
                status_data["message"] = f"Services unavailable: {', '.join(failed_services)}"
            
            # Publish status as JSON string
            status_msg = String()
            status_msg.data = json.dumps(status_data)
            self.status_pub.publish(status_msg)
            
            rospy.loginfo(f"Published status: {status_data['message']}")
            
        except Exception as e:
            rospy.logerr(f"Error publishing status: {str(e)}")
            # Publish error status
            error_status = {
                "timestamp": rospy.Time.now().to_sec(),
                "success": False,
                "message": f"Status publisher error: {str(e)}",
                "error": str(e)
            }
            status_msg = String()
            status_msg.data = json.dumps(error_status)
            self.status_pub.publish(status_msg)

if __name__ == '__main__':
    try:
        publisher = SystemStatusPublisher()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr(f"System status publisher failed: {str(e)}")
