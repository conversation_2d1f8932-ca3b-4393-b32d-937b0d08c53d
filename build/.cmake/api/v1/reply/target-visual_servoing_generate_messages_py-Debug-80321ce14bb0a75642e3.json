{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "include", "generate_messages", "add_dependencies"], "files": ["build/visual_servoing/cmake/visual_servoing-genmsg.cmake", "/opt/ros/noetic/share/genmsg/cmake/genmsg-extras.cmake", "visual_servoing/CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 2, "file": 2, "line": 79, "parent": 0}, {"command": 1, "file": 1, "line": 307, "parent": 1}, {"file": 0, "parent": 2}, {"command": 0, "file": 0, "line": 178, "parent": 3}, {"command": 3, "file": 0, "line": 260, "parent": 3}, {"command": 3, "file": 0, "line": 185, "parent": 3}]}, "dependencies": [{"backtrace": 5, "id": "std_msgs_generate_messages_py::@49918bdc369c3657be32"}, {"backtrace": 6, "id": "_visual_servoing_generate_messages_check_deps_ParameterReloadStatus::@1e09fb0d76ac329dfbf9"}], "id": "visual_servoing_generate_messages_py::@1e09fb0d76ac329dfbf9", "name": "visual_servoing_generate_messages_py", "paths": {"build": "visual_servoing", "source": "visual_servoing"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2, 3]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "build/visual_servoing/CMakeFiles/visual_servoing_generate_messages_py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/visual_servoing/CMakeFiles/visual_servoing_generate_messages_py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/devel/lib/python3/dist-packages/visual_servoing/msg/_ParameterReloadStatus.py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/devel/lib/python3/dist-packages/visual_servoing/msg/__init__.py.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}