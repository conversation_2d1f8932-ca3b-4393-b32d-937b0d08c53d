import React, { useState, useEffect, useContext } from 'react';
import { RosContext } from './RosConnection';
import './ReloadStatusMonitor.css';

const ReloadStatusMonitor = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [reloadStatus, setReloadStatus] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    if (!isConnected || !ros || !window.ROSLIB) return;

    // Subscribe to a single reload status topic
    const reloadStatusTopic = new window.ROSLIB.Topic({
      ros: ros,
      name: '/system/reload_status',
      messageType: 'std_msgs/String'
    });

    reloadStatusTopic.subscribe((message) => {
      try {
        const statusData = JSON.parse(message.data);
        console.log('Reload status received:', statusData);
        setReloadStatus(statusData);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error parsing reload status:', error);
        setReloadStatus({
          success: false,
          message: 'Error parsing status data',
          timestamp: new Date().toISOString()
        });
        setLastUpdate(new Date());
      }
    });

    // Cleanup subscription
    return () => {
      reloadStatusTopic.unsubscribe();
    };
  }, [ros, isConnected]);



  const getStatusIcon = (success) => {
    return success ? '✅' : '❌';
  };

  const getStatusColor = (success) => {
    return success ? '#4CAF50' : '#f44336';
  };

  if (!isConnected) {
    return (
      <div className="reload-status-monitor">
        <h3>🔄 Parameter Reload Status</h3>
        <div className="status-disconnected">
          <p>⚠️ Not connected to ROS</p>
        </div>
      </div>
    );
  }

  return (
    <div className="reload-status-monitor">
      <h3>🔄 Parameter Reload Status</h3>

      {lastUpdate && (
        <div className="last-update">
          Last update: {lastUpdate.toLocaleTimeString()}
        </div>
      )}

      <div className="status-card">
        <h4>🔄 System Reload Status</h4>
        {reloadStatus ? (
          <div className="status-content">
            <div className="status-header">
              <span className="status-icon" style={{ color: getStatusColor(reloadStatus.success) }}>
                {getStatusIcon(reloadStatus.success)}
              </span>
              <span className="status-text">
                {reloadStatus.success ? 'Active' : 'Error'}
              </span>
            </div>

            <div className="status-details">
              <div className="detail-row">
                <strong>Status:</strong>
                <span>{reloadStatus.message || 'No message'}</span>
              </div>
              <div className="detail-row">
                <strong>Timestamp:</strong>
                <span>{reloadStatus.timestamp ? new Date(reloadStatus.timestamp).toLocaleString() : 'Unknown'}</span>
              </div>
              {reloadStatus.node && (
                <div className="detail-row">
                  <strong>Node:</strong>
                  <span>{reloadStatus.node}</span>
                </div>
              )}
              {reloadStatus.config_file && (
                <div className="detail-row">
                  <strong>Config File:</strong>
                  <span>{reloadStatus.config_file}</span>
                </div>
              )}
              {reloadStatus.error && (
                <div className="detail-row error">
                  <strong>Error:</strong>
                  <span>{reloadStatus.error}</span>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="status-waiting">
            <p>⏳ Waiting for status...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReloadStatusMonitor;
