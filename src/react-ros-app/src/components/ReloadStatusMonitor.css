.reload-status-monitor {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reload-status-monitor h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2em;
}

.last-update {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 15px;
  text-align: right;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
}

.status-card {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  background: #fafafa;
}

.status-card h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1em;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.status-icon {
  font-size: 1.2em;
  font-weight: bold;
}

.status-text {
  font-weight: 600;
  font-size: 1em;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: white;
  border-radius: 3px;
  border: 1px solid #f0f0f0;
  font-size: 0.9em;
}

.detail-row strong {
  color: #555;
  min-width: 120px;
  text-align: left;
}

.detail-row span {
  color: #333;
  text-align: right;
  word-break: break-word;
  max-width: 200px;
}

.detail-row.error {
  background: #ffebee;
  border-color: #ffcdd2;
}

.detail-row.error strong {
  color: #c62828;
}

.detail-row.error span {
  color: #d32f2f;
  font-size: 0.85em;
}

.status-waiting {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.status-disconnected {
  text-align: center;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
}

.status-disconnected p {
  margin: 0;
  font-weight: 500;
}

/* Animation for status updates */
.status-card {
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Success/Error color coding */
.status-success {
  border-left: 4px solid #4CAF50;
}

.status-error {
  border-left: 4px solid #f44336;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .reload-status-monitor {
    padding: 15px;
    margin: 10px 0;
  }
  
  .status-card {
    padding: 12px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-row strong {
    min-width: auto;
  }
  
  .detail-row span {
    text-align: left;
    max-width: 100%;
  }
}
