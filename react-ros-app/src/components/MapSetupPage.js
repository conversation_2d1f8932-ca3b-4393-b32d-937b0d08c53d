import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './MapSetupPage.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const MapSetupPage = ({ onMapSetupComplete, onBack }) => {
  const [mapChoice, setMapChoice] = useState(null); // 'existing' or 'new'
  const [existingMaps, setExistingMaps] = useState([]);
  const [selectedMap, setSelectedMap] = useState(null);
  const [newMapData, setNewMapData] = useState({
    boundary: [],
    startPoint: null,
    endPoint: null,
    name: ''
  });
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingMode, setDrawingMode] = useState(null); // 'boundary', 'start', 'end'
  const [currentZoom, setCurrentZoom] = useState(18);
  const [currentStep, setCurrentStep] = useState(1); // 1: boundary, 2: points, 3: name, 4: save
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load existing maps from config directory
    loadExistingMaps();
  }, []);

  const loadExistingMaps = async () => {
    const maps = [];

    // Known GeoJSON files that we copied to public directory
    const knownFiles = [
      'boundaries.geojson',
      'field_north.geojson',
      'field_south.geojson',
      'test_field.geojson'
    ];

    console.log('Loading existing maps...');

    // Try to load each known config file
    for (const filename of knownFiles) {
      try {
        console.log(`Attempting to load: ${filename}`);
        const response = await fetch(`/weednix_sensors/config/${filename}`);

        if (response.ok) {
          const geoJsonData = await response.json();
          console.log(`Successfully loaded ${filename}:`, geoJsonData);

          // Validate GeoJSON structure
          if (geoJsonData.features && geoJsonData.features.length > 0) {
            const feature = geoJsonData.features[0];

            // Extract map name from various sources
            const mapName = geoJsonData.name ||
                           feature.properties?.name ||
                           filename.replace('.geojson', '').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Extract boundary coordinates
            const boundary = feature.geometry?.coordinates?.[0] || [];

            // Only add if we have valid boundary data
            if (boundary.length >= 3) {
              maps.push({
                id: filename.replace('.geojson', ''),
                name: mapName,
                filename: filename,
                data: geoJsonData,
                boundary: boundary,
                properties: feature.properties || {},
                source: 'config'
              });
              console.log(`Added map: ${mapName} with ${boundary.length} boundary points`);
            } else {
              console.warn(`Invalid boundary data for ${filename}: ${boundary.length} points`);
            }
          } else {
            console.warn(`Invalid GeoJSON structure for ${filename}`);
          }
        } else {
          console.log(`File not found: ${filename} (${response.status})`);
        }
      } catch (error) {
        console.warn(`Error loading ${filename}:`, error.message);
      }
    }

    // Also check localStorage for user-created maps
    try {
      const savedMaps = JSON.parse(localStorage.getItem('savedMaps') || '[]');
      const userMaps = savedMaps.map(map => ({
        ...map,
        source: 'user'
      }));

      console.log(`Loaded ${userMaps.length} user-created maps from localStorage`);
      setExistingMaps([...maps, ...userMaps]);
    } catch (error) {
      console.warn('Error loading user maps from localStorage:', error);
      setExistingMaps(maps);
    }

    console.log(`Total maps loaded: ${maps.length} config maps`);
  };

  const MapClickHandler = () => {
    useMapEvents({
      click: (e) => {
        if (!isDrawing) return;

        const { lat, lng } = e.latlng;
        console.log(`Map clicked: ${lat}, ${lng}, mode: ${drawingMode}`);

        if (drawingMode === 'boundary') {
          setNewMapData(prev => {
            const newBoundary = [...prev.boundary, [lng, lat]];
            console.log(`Added boundary point: [${lng}, ${lat}], total points: ${newBoundary.length}`);
            return {
              ...prev,
              boundary: newBoundary
            };
          });
        } else if (drawingMode === 'start') {
          setNewMapData(prev => ({
            ...prev,
            startPoint: [lng, lat]
          }));
          console.log(`Set start point: [${lng}, ${lat}]`);
          setDrawingMode(null);
          setIsDrawing(false);
        } else if (drawingMode === 'end') {
          setNewMapData(prev => ({
            ...prev,
            endPoint: [lng, lat]
          }));
          console.log(`Set end point: [${lng}, ${lat}]`);
          setDrawingMode(null);
          setIsDrawing(false);
        }
      },
      zoomend: (e) => {
        setCurrentZoom(e.target.getZoom());
      }
    });
    return null;
  };

  const startDrawing = (mode) => {
    setDrawingMode(mode);
    setIsDrawing(true);
  };

  const finishBoundary = () => {
    if (newMapData.boundary.length >= 3) {
      // Close the polygon by adding the first point at the end (only if not already closed)
      setNewMapData(prev => {
        const boundary = prev.boundary;
        const firstPoint = boundary[0];
        const lastPoint = boundary[boundary.length - 1];

        // Check if polygon is already closed (first and last points are the same)
        const isAlreadyClosed = firstPoint && lastPoint &&
                               Math.abs(firstPoint[0] - lastPoint[0]) < 0.000001 &&
                               Math.abs(firstPoint[1] - lastPoint[1]) < 0.000001;

        console.log(`Finishing boundary: ${boundary.length} points, already closed: ${isAlreadyClosed}`);

        if (!isAlreadyClosed && firstPoint) {
          console.log(`Adding closing point: [${firstPoint[0]}, ${firstPoint[1]}]`);
          return {
            ...prev,
            boundary: [...boundary, firstPoint]
          };
        }

        console.log('Boundary already closed or invalid, not adding closing point');
        return prev; // Already closed, no need to add duplicate point
      });
      setDrawingMode(null);
      setIsDrawing(false);
      setCurrentStep(2); // Move to next step: set points
    }
  };

  const nextStep = () => {
    if (currentStep === 2 && newMapData.startPoint && newMapData.endPoint) {
      setCurrentStep(3); // Move to name input
    } else if (currentStep === 3 && newMapData.name.trim()) {
      setCurrentStep(4); // Move to save step
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setIsDrawing(false);
      setDrawingMode(null);
    }
  };

  const clearDrawing = () => {
    setNewMapData({
      boundary: [],
      startPoint: null,
      endPoint: null,
      name: ''
    });
    setIsDrawing(false);
    setDrawingMode(null);
    setCurrentStep(1); // Reset to first step
    setIsSaving(false);
  };

  const saveNewMap = async () => {
    if (!newMapData.name.trim()) {
      alert('Please provide a map name');
      return;
    }

    if (newMapData.boundary.length < 4) {
      alert('Please complete the boundary drawing (minimum 3 points + closing point)');
      return;
    }

    setIsSaving(true);

    // Use the map name as filename (no need to ask user)
    const customName = newMapData.name.toLowerCase().replace(/\s+/g, '_');

    // Sanitize filename
    const sanitizedName = customName.replace(/[^a-zA-Z0-9_-]/g, '_');
    const filename = `${sanitizedName}.geojson`;

    // Calculate approximate area (simple polygon area calculation)
    const calculatePolygonArea = (coordinates) => {
      let area = 0;
      const n = coordinates.length;
      for (let i = 0; i < n - 1; i++) {
        area += coordinates[i][0] * coordinates[i + 1][1];
        area -= coordinates[i + 1][0] * coordinates[i][1];
      }
      return Math.abs(area / 2);
    };

    const approximateArea = calculatePolygonArea(newMapData.boundary);
    const areaInHectares = (approximateArea * 111000 * 111000 / 10000).toFixed(2); // Rough conversion

    // Create GeoJSON structure with enhanced metadata
    const geoJsonData = {
      type: "FeatureCollection",
      name: newMapData.name,
      features: [
        {
          type: "Feature",
          properties: {
            name: newMapData.name,
            filename: filename,
            created: new Date().toISOString(),
            creator: "Weednix Map Creator",
            area: `${areaInHectares} hectares (approximate)`,
            boundary_points: newMapData.boundary.length,
            startPoint: newMapData.startPoint,
            endPoint: newMapData.endPoint,
            description: `Field boundary created on ${new Date().toLocaleDateString()}`
          },
          geometry: {
            coordinates: [newMapData.boundary],
            type: "Polygon"
          }
        }
      ]
    };

    const mapToSave = {
      id: Date.now().toString(),
      name: newMapData.name,
      filename: filename,
      data: geoJsonData,
      boundary: newMapData.boundary,
      startPoint: newMapData.startPoint,
      endPoint: newMapData.endPoint,
      createdAt: new Date().toISOString(),
      properties: {
        area: `${areaInHectares} hectares (approximate)`,
        boundary_points: newMapData.boundary.length
      },
      source: 'user'
    };

    try {
      // Get the current host and use port 3001 for the API server
      const apiHost = window.location.hostname;
      const apiUrl = `http://${apiHost}:3001/api/save-geojson`;

      // Save to server
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: filename,
          data: geoJsonData
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save map to server');
      }

      const result = await response.json();
      console.log('Map saved to server:', result);

      // Save to localStorage as backup
      const savedMaps = JSON.parse(localStorage.getItem('savedMaps') || '[]');
      savedMaps.push(mapToSave);
      localStorage.setItem('savedMaps', JSON.stringify(savedMaps));

      // Show success message
      alert(`Map "${newMapData.name}" saved successfully!\n\nArea: ${areaInHectares} hectares (approximate)\nBoundary points: ${newMapData.boundary.length}\n\nSaved to: weednix_sensors/config/${filename}`);

      // Wait a moment for file to be fully written, then trigger boundary reload
      console.log('Waiting for file to be fully written before triggering reload...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

      console.log('Triggering boundary reload for newly created map...');
      const reloadSuccess = await triggerBoundaryReload(filename);

      if (!reloadSuccess) {
        console.warn('Boundary reload failed for newly created map');
        alert('Warning: Map was saved but boundary reload failed. The geofencing node may need to be restarted.');
      }

      setExistingMaps(prev => [...prev, mapToSave]);
      setSelectedMap(mapToSave);
      setMapChoice('existing');

    } catch (error) {
      console.error('Error saving map:', error);
      alert(`Failed to save map: ${error.message}\n\nPlease try again or contact support.`);
    } finally {
      setIsSaving(false);
    }
  };

  const downloadGeoJSON = (geoJsonData, filename) => {
    const dataStr = JSON.stringify(geoJsonData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`Downloaded GeoJSON file: ${filename}`);
  };

  const triggerBoundaryReload = async (geoJsonFilename) => {
    try {
      const apiHost = window.location.hostname;
      const reloadUrl = `http://${apiHost}:3001/api/reload-boundary`;

      console.log(`Triggering boundary reload for: ${geoJsonFilename}`);

      const response = await fetch(reloadUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          geoJsonFile: geoJsonFilename
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to trigger boundary reload');
      }

      const result = await response.json();
      console.log('Boundary reload triggered:', result);

      // Show success notification
      if (result.success) {
        console.log(`✅ Boundary reloaded successfully: ${result.message}`);
        // Don't show alert for successful reloads to avoid interrupting user flow
      } else {
        console.warn(`⚠️ Boundary reload failed: ${result.message}`);
        alert(`Warning: Boundary reload failed:\n\n${result.message}`);
      }

      return result.success;

    } catch (error) {
      console.error('Error triggering boundary reload:', error);
      alert(`Warning: Failed to reload boundary in geofencing node: ${error.message}\n\nPlease check the connection and try again.`);
      return false;
    }
  };

  const handleMapSelect = async (map) => {
    console.log(`Map selected: ${map.name} (${map.filename})`);
    setSelectedMap(map);

    // Immediately trigger boundary reload for the selected map
    if (map.filename) {
      console.log(`Triggering boundary reload for selected map: ${map.filename}`);
      const reloadSuccess = await triggerBoundaryReload(map.filename);

      if (reloadSuccess) {
        console.log(`✅ Boundary successfully reloaded for map: ${map.name}`);
      } else {
        console.warn(`⚠️ Failed to reload boundary for map: ${map.name}`);
      }
    } else {
      console.warn('Selected map has no filename, cannot trigger boundary reload');
    }
  };

  const handleContinue = async () => {
    if (mapChoice === 'existing' && selectedMap) {
      // For existing maps, trigger boundary reload before continuing
      if (selectedMap.filename) {
        console.log('Triggering boundary reload for existing map:', selectedMap.filename);
        await triggerBoundaryReload(selectedMap.filename);
      } else {
        console.warn('Selected map has no filename, skipping boundary reload');
      }

      onMapSetupComplete({
        type: 'existing',
        mapData: selectedMap
      });
    } else if (mapChoice === 'new' && newMapData.boundary.length >= 4) {
      onMapSetupComplete({
        type: 'new',
        mapData: newMapData
      });
    }
  };

  const canContinue = () => {
    return (mapChoice === 'existing' && selectedMap) || 
           (mapChoice === 'new' && newMapData.boundary.length >= 4);
  };

  return (
    <div className="map-setup-page">
      <header className="map-setup-header">
        <button className="back-button" onClick={onBack}>← Back</button>
        <h1>Mission Map Setup</h1>
        <p>Choose an existing map or create a new field boundary</p>
      </header>

      {!mapChoice && (
        <div className="map-choice-container">
          <div className="choice-cards">
            <div className="choice-card" onClick={() => setMapChoice('existing')}>
              <div className="choice-icon">📋</div>
              <h3>Use Existing Map</h3>
              <p>Select from previously saved field boundaries</p>
            </div>
            <div className="choice-card" onClick={() => setMapChoice('new')}>
              <div className="choice-icon">✏️</div>
              <h3>Create New Map</h3>
              <p>Draw a new field boundary on the map</p>
            </div>
          </div>
        </div>
      )}

      {mapChoice === 'existing' && (
        <div className="existing-maps-container">
          <h3>Select Existing Map ({existingMaps.length} available)</h3>
          <div className="maps-list">
            {existingMaps.map(map => (
              <div
                key={map.id}
                className={`map-item ${selectedMap?.id === map.id ? 'selected' : ''} ${map.source}`}
                onClick={() => handleMapSelect(map)}
              >
                <div className="map-item-header">
                  <h4>{map.name}</h4>
                  <div className="map-item-actions">
                    <span className={`source-badge ${map.source}`}>
                      {map.source === 'config' ? '📁 Config' : '👤 User'}
                    </span>
                  </div>
                </div>
                <div className="map-item-details">
                  <p><strong>Boundary points:</strong> {map.boundary?.length || 0}</p>
                  {map.properties?.area && <p><strong>Area:</strong> {map.properties.area}</p>}
                  {map.properties?.crop_type && <p><strong>Crop:</strong> {map.properties.crop_type}</p>}
                  {map.filename && <p><strong>File:</strong> {map.filename}</p>}
                </div>
                {map.createdAt && (
                  <small>Created: {new Date(map.createdAt).toLocaleDateString()}</small>
                )}
                {map.source === 'config' && (
                  <small>Source: Config Directory</small>
                )}
              </div>
            ))}
          </div>

          {existingMaps.length === 0 && (
            <div className="no-maps-message">
              <p>No existing maps found. Create a new map to get started.</p>
              <button onClick={() => setMapChoice('new')} className="create-new-btn">
                Create New Map
              </button>
            </div>
          )}
          
          {selectedMap && (
            <div className="map-preview">
              <h4>Map Preview: {selectedMap.name}</h4>
              <MapContainer
                center={[29.9778, 30.9473]} // Default center, will be adjusted based on boundary
                zoom={18}
                minZoom={1}
                maxZoom={25}
                style={{ height: '300px', width: '100%' }}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  maxZoom={25}
                />
                {selectedMap.boundary && selectedMap.boundary.length > 0 && (
                  <Polygon
                    positions={selectedMap.boundary.map(coord => [coord[1], coord[0]])}
                    color="blue"
                    fillColor="lightblue"
                    fillOpacity={0.3}
                  />
                )}
                {selectedMap.startPoint && (
                  <Marker
                    position={[selectedMap.startPoint[1], selectedMap.startPoint[0]]}
                    icon={L.divIcon({
                      className: 'start-point',
                      html: '<div style="background: green; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; color: white; text-align: center; line-height: 12px; font-size: 8px;">S</div>',
                      iconSize: [16, 16],
                      iconAnchor: [8, 8]
                    })}
                  />
                )}
                {selectedMap.endPoint && (
                  <Marker
                    position={[selectedMap.endPoint[1], selectedMap.endPoint[0]]}
                    icon={L.divIcon({
                      className: 'end-point',
                      html: '<div style="background: blue; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; color: white; text-align: center; line-height: 12px; font-size: 8px;">E</div>',
                      iconSize: [16, 16],
                      iconAnchor: [8, 8]
                    })}
                  />
                )}
              </MapContainer>
            </div>
          )}
        </div>
      )}

      {mapChoice === 'new' && (
        <div className="new-map-container">
          <div className="step-indicator">
            <div className={`step ${currentStep >= 1 ? 'active' : ''} ${currentStep > 1 ? 'completed' : ''}`}>
              <span className="step-number">1</span>
              <span className="step-label">Draw Boundary</span>
            </div>
            <div className={`step ${currentStep >= 2 ? 'active' : ''} ${currentStep > 2 ? 'completed' : ''}`}>
              <span className="step-number">2</span>
              <span className="step-label">Set Points</span>
            </div>
            <div className={`step ${currentStep >= 3 ? 'active' : ''} ${currentStep > 3 ? 'completed' : ''}`}>
              <span className="step-number">3</span>
              <span className="step-label">Name Map</span>
            </div>
            <div className={`step ${currentStep >= 4 ? 'active' : ''}`}>
              <span className="step-number">4</span>
              <span className="step-label">Save</span>
            </div>
          </div>

          <div className="step-content-container">
            {/* Step 1: Draw Boundary */}
            {currentStep === 1 && (
              <div className="step-content">
                <h3>Step 1: Draw Field Boundary</h3>
                <p>Click on the map to draw the boundary of your field. You need at least 3 points to create a valid boundary.</p>

                <div className="drawing-status">
                  {drawingMode === 'boundary' ? (
                    <div className="status-message active">
                      🖱️ Click on the map to add boundary points ({newMapData.boundary.length} points added)
                    </div>
                  ) : (
                    <div className="status-message">
                      {newMapData.boundary.length === 0 ?
                        '📍 Click "Start Drawing" to begin' :
                        `✅ Boundary has ${newMapData.boundary.length} points ${newMapData.boundary.length >= 3 ? '(ready to finish)' : '(need at least 3 points)'}`
                      }
                    </div>
                  )}
                </div>

                <div className="control-buttons">
                  {newMapData.boundary.length === 0 ? (
                    <button
                      className="draw-btn primary"
                      onClick={() => startDrawing('boundary')}
                    >
                      🖊️ Start Drawing Boundary
                    </button>
                  ) : (
                    <>
                      <button
                        className={`draw-btn ${drawingMode === 'boundary' ? 'active' : ''}`}
                        onClick={() => startDrawing('boundary')}
                        disabled={isDrawing && drawingMode !== 'boundary'}
                      >
                        {drawingMode === 'boundary' ? 'Drawing...' : 'Continue Drawing'}
                      </button>

                      {newMapData.boundary.length >= 3 && (
                        <button className="finish-btn primary" onClick={finishBoundary}>
                          ✅ Finish Boundary ({newMapData.boundary.length} points)
                        </button>
                      )}

                      <button className="clear-btn" onClick={clearDrawing}>
                        🗑️ Clear All
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Step 2: Set Start and End Points */}
            {currentStep === 2 && (
              <div className="step-content">
                <h3>Step 2: Set Start and End Points</h3>
                <p>Click on the map to set where the robot should start and end its mission.</p>

                <div className="drawing-status">
                  {drawingMode === 'start' && (
                    <div className="status-message active">
                      📍 Click on the map to set the start point
                    </div>
                  )}
                  {drawingMode === 'end' && (
                    <div className="status-message active">
                      🏁 Click on the map to set the end point
                    </div>
                  )}
                  {!isDrawing && (
                    <div className="status-message">
                      {newMapData.startPoint ? '✅ Start point set' : '❌ Start point needed'} |
                      {newMapData.endPoint ? ' ✅ End point set' : ' ❌ End point needed'}
                    </div>
                  )}
                </div>

                <div className="control-buttons">
                  <button
                    className={`draw-btn ${drawingMode === 'start' ? 'active' : ''} ${newMapData.startPoint ? 'completed' : ''}`}
                    onClick={() => startDrawing('start')}
                    disabled={isDrawing && drawingMode !== 'start'}
                  >
                    📍 {newMapData.startPoint ? 'Change Start Point' : 'Set Start Point'}
                  </button>

                  <button
                    className={`draw-btn ${drawingMode === 'end' ? 'active' : ''} ${newMapData.endPoint ? 'completed' : ''}`}
                    onClick={() => startDrawing('end')}
                    disabled={isDrawing && drawingMode !== 'end'}
                  >
                    🏁 {newMapData.endPoint ? 'Change End Point' : 'Set End Point'}
                  </button>

                  <button className="back-btn" onClick={prevStep}>
                    ← Back to Boundary
                  </button>

                  {newMapData.startPoint && newMapData.endPoint && (
                    <button className="next-btn primary" onClick={nextStep}>
                      Next: Name Map →
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Name the Map */}
            {currentStep === 3 && (
              <div className="step-content">
                <h3>Step 3: Name Your Map</h3>
                <p>Give your field map a descriptive name.</p>

                <div className="form-group">
                  <label htmlFor="mapName">Map Name</label>
                  <input
                    type="text"
                    id="mapName"
                    value={newMapData.name}
                    onChange={(e) => setNewMapData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter field name (e.g., North Field, Test Area)"
                    className="map-name-input"
                    autoFocus
                  />
                </div>

                <div className="control-buttons">
                  <button className="back-btn" onClick={prevStep}>
                    ← Back to Points
                  </button>

                  {newMapData.name.trim() && (
                    <button className="next-btn primary" onClick={nextStep}>
                      Next: Save Map →
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Step 4: Save the Map */}
            {currentStep === 4 && (
              <div className="step-content">
                <h3>Step 4: Save Your Map</h3>
                <p>Review your map details and save it to the system.</p>

                <div className="map-summary">
                  <h4>Map Summary:</h4>
                  <ul>
                    <li><strong>Name:</strong> {newMapData.name}</li>
                    <li><strong>Boundary Points:</strong> {newMapData.boundary.length}</li>
                    <li><strong>Start Point:</strong> {newMapData.startPoint ? '✅ Set' : '❌ Not set'}</li>
                    <li><strong>End Point:</strong> {newMapData.endPoint ? '✅ Set' : '❌ Not set'}</li>
                  </ul>
                </div>

                <div className="control-buttons">
                  <button className="back-btn" onClick={prevStep}>
                    ← Back to Name
                  </button>

                  <button
                    className="save-btn primary"
                    onClick={saveNewMap}
                    disabled={isSaving}
                  >
                    {isSaving ? '💾 Saving...' : '💾 Save Map'}
                  </button>

                  <button className="clear-btn" onClick={clearDrawing}>
                    🗑️ Start Over
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="map-container">
            <div className="map-instructions">
              <p><strong>🗺️ Map Controls:</strong> Use mouse wheel or +/- buttons to zoom (levels 1-25). Current zoom: <span className="zoom-level">{currentZoom}</span></p>
              {drawingMode === 'boundary' && (
                <p><strong>🖱️ Drawing Mode:</strong> Click on the map to add boundary points. Need at least 3 points to create a valid boundary.</p>
              )}
              {!isDrawing && newMapData.boundary.length > 0 && (
                <p><strong>📍 Boundary Status:</strong> {newMapData.boundary.length} points added. {newMapData.boundary.length >= 3 ? 'Ready to finish!' : `Need ${3 - newMapData.boundary.length} more points.`}</p>
              )}
            </div>
            <MapContainer
              center={[29.9778, 30.9473]}
              zoom={18}
              minZoom={1}
              maxZoom={25}
              style={{ height: '500px', width: '100%' }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                maxZoom={25}
              />
              <MapClickHandler />
              
              {/* Show boundary points as markers while drawing */}
              {newMapData.boundary.map((coord, index) => {
                const boundaryIcon = L.divIcon({
                  className: 'boundary-point',
                  html: '<div style="background: red; width: 8px; height: 8px; border-radius: 50%; border: 2px solid white;"></div>',
                  iconSize: [12, 12],
                  iconAnchor: [6, 6]
                });

                return (
                  <Marker
                    key={`boundary-${index}`}
                    position={[coord[1], coord[0]]}
                    icon={boundaryIcon}
                  />
                );
              })}

              {/* Show polygon when we have enough points */}
              {newMapData.boundary.length > 2 && (
                <Polygon
                  positions={newMapData.boundary.map(coord => [coord[1], coord[0]])}
                  color="red"
                  fillColor="lightcoral"
                  fillOpacity={0.3}
                  weight={3}
                />
              )}

              {/* Show line from last point to current mouse position while drawing */}
              {newMapData.boundary.length > 0 && drawingMode === 'boundary' && (
                <Polygon
                  positions={newMapData.boundary.map(coord => [coord[1], coord[0]])}
                  color="red"
                  fillOpacity={0}
                  weight={2}
                  dashArray="5, 5"
                />
              )}
              
              {newMapData.startPoint && (
                <Marker
                  position={[newMapData.startPoint[1], newMapData.startPoint[0]]}
                  icon={L.divIcon({
                    className: 'start-point',
                    html: '<div style="background: green; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;">S</div>',
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                  })}
                />
              )}

              {newMapData.endPoint && (
                <Marker
                  position={[newMapData.endPoint[1], newMapData.endPoint[0]]}
                  icon={L.divIcon({
                    className: 'end-point',
                    html: '<div style="background: blue; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;">E</div>',
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                  })}
                />
              )}
            </MapContainer>
          </div>
        </div>
      )}

      {mapChoice && (
        <div className="map-setup-footer">
          <button className="back-choice-btn" onClick={() => setMapChoice(null)}>
            ← Change Choice
          </button>
          <button 
            className="continue-btn" 
            onClick={handleContinue}
            disabled={!canContinue()}
          >
            Continue to Mission Parameters →
          </button>
        </div>
      )}
    </div>
  );
};

export default MapSetupPage;
