import React, { useState, useEffect } from 'react';
import './App.css';
import RosConnection from './components/RosConnection';
import JoystickControl from './components/JoystickControl';
import MapVisualization from './components/MapVisualization';
import RobotStatus from './components/RobotStatus';
import ModeControl from './components/ModeControl';
import ModeSelectionPage from './components/ModeSelectionPage';
import MapSetupPage from './components/MapSetupPage';
import MissionParametersForm from './components/MissionParametersForm';
import ReloadStatusMonitor from './components/ReloadStatusMonitor';

function App() {
  const [rosUrl, setRosUrl] = useState(process.env.REACT_APP_DEFAULT_ROS_URL || 'ws://localhost:9090');
  const [isConfiguring, setIsConfiguring] = useState(true);
  const [networkInfo, setNetworkInfo] = useState(null);
  const [robotMode, setRobotMode] = useState('manual'); // 'manual' or 'autonomous'

  // New state for mission flow
  const [appState, setAppState] = useState('mode-selection'); // 'mode-selection', 'map-setup', 'mission-parameters', 'monitoring'
  const [operationMode, setOperationMode] = useState(null); // 'mission' or 'free'
  const [missionData, setMissionData] = useState(null);

  // Get network information on component mount
  useEffect(() => {
    const getNetworkInfo = () => {
      const hostname = window.location.hostname;
      const port = window.location.port || '3000';
      const protocol = window.location.protocol;

      setNetworkInfo({
        hostname,
        port,
        protocol,
        fullUrl: `${protocol}//${hostname}:${port}`,
        isLocalhost: hostname === 'localhost' || hostname === '127.0.0.1'
      });
    };

    getNetworkInfo();
  }, []);

  const handleConnect = (e) => {
    e.preventDefault();
    setIsConfiguring(false);
  };

  const generateRobotIpUrl = () => {
    const robotIp = process.env.REACT_APP_ROBOT_IP || 'localhost';
    return `ws://${robotIp}:9090`;
  };

  const handleModeChange = (newMode) => {
    setRobotMode(newMode);
    console.log(`Robot mode changed to: ${newMode}`);
  };

  // New handlers for mission flow
  const handleModeSelect = (mode) => {
    setOperationMode(mode);
    if (mode === 'mission') {
      setAppState('map-setup');
    } else {
      // For free mode, skip map setup and go directly to monitoring
      setAppState('monitoring');
      setIsConfiguring(false);
    }
  };

  const handleMapSetupComplete = (mapSetupData) => {
    setMissionData(prev => ({
      ...prev,
      mapData: mapSetupData.mapData
    }));
    setAppState('mission-parameters');
  };

  const handleLaunchMission = (missionConfig) => {
    setMissionData(missionConfig);
    setAppState('monitoring');
    setIsConfiguring(false);
    console.log('Mission launched with config:', missionConfig);
  };

  const handleBackToModeSelection = () => {
    setAppState('mode-selection');
    setOperationMode(null);
    setMissionData(null);
  };

  const handleBackToMapSetup = () => {
    setAppState('map-setup');
  };

  // Render different pages based on app state
  const renderCurrentPage = () => {
    if (appState === 'mode-selection') {
      return <ModeSelectionPage onModeSelect={handleModeSelect} />;
    }

    if (appState === 'map-setup') {
      return (
        <MapSetupPage
          onMapSetupComplete={handleMapSetupComplete}
          onBack={handleBackToModeSelection}
        />
      );
    }

    if (appState === 'mission-parameters') {
      return (
        <MissionParametersForm
          mapData={missionData?.mapData}
          onLaunchMission={handleLaunchMission}
          onBack={handleBackToMapSetup}
        />
      );
    }

    // Default monitoring page (for both mission and free mode)
    return null;
  };

  return (
    <div className="App">
      {appState !== 'monitoring' ? (
        renderCurrentPage()
      ) : (
        <>
          <header className="App-header">
            <h1>
              {operationMode === 'mission' ? '🗺️ Mission Mode' : '🔓 Free Mode'} - ROS Robot Control Interface
            </h1>
            {operationMode === 'mission' && missionData && (
              <div className="mission-info">
                <span>Mission: {missionData.mapData?.name || 'Active Mission'}</span>
                <button onClick={handleBackToModeSelection} className="change-mode-btn">
                  Change Mode
                </button>
              </div>
            )}
          </header>

          {isConfiguring ? (
        <div className="connection-form">
          <h2>Connect to ROS Bridge Server</h2>

          {/* Network Information */}
          {networkInfo && process.env.REACT_APP_SHOW_NETWORK_INFO === 'true' && (
            <div className="network-info">
              <h3>Network Access Information</h3>
              <div className="info-section">
                <p><strong>Current URL:</strong> {networkInfo.fullUrl}</p>
                {!networkInfo.isLocalhost && (
                  <div className="mobile-access">
                    <p><strong>📱 Mobile Access:</strong></p>
                    <p>Open this URL on your phone: <code>{networkInfo.fullUrl}</code></p>
                    <p>Make sure your phone is connected to the same WiFi network.</p>
                  </div>
                )}
                {networkInfo.isLocalhost && (
                  <div className="localhost-warning">
                    <p><strong>⚠️ Local Access Only:</strong></p>
                    <p>Currently accessible only on this device. To enable mobile access:</p>
                    <ol>
                      <li>Stop the server (Ctrl+C)</li>
                      <li>Run: <code>npm start</code> (instead of <code>npm run start-local</code>)</li>
                      <li>The server will be accessible from other devices on your network</li>
                    </ol>
                  </div>
                )}
              </div>
            </div>
          )}

          <form onSubmit={handleConnect}>
            <div className="form-group">
              <label htmlFor="rosUrl">ROS Bridge URL:</label>
              <input
                type="text"
                id="rosUrl"
                value={rosUrl}
                onChange={(e) => setRosUrl(e.target.value)}
                placeholder={generateRobotIpUrl()}
              />
              <small>
                Default: {generateRobotIpUrl()}
                {networkInfo && !networkInfo.isLocalhost &&
                  ` | For mobile access, use: ws://${networkInfo.hostname}:9090`
                }
              </small>
            </div>

            <div className="quick-connect">
              <p><strong>Quick Connect:</strong></p>
              <button
                type="button"
                onClick={() => setRosUrl('ws://localhost:9090')}
                className="quick-btn"
              >
                Local (localhost:9090)
              </button>
              {networkInfo && !networkInfo.isLocalhost && (
                <button
                  type="button"
                  onClick={() => setRosUrl(`ws://${networkInfo.hostname}:9090`)}
                  className="quick-btn"
                >
                  Network ({networkInfo.hostname}:9090)
                </button>
              )}
            </div>

            <button type="submit" className="connect-btn">Connect to Robot</button>
          </form>
        </div>
      ) : (
        <RosConnection url={rosUrl}>
          <div className="connection-status">
            <button onClick={() => setIsConfiguring(true)}>
              Change Connection
            </button>
          </div>
          <div className="dashboard">
            <div className="dashboard-column">
              <ModeControl onModeChange={handleModeChange} />
              <JoystickControl robotMode={robotMode} />
              <RobotStatus />
              <ReloadStatusMonitor />
            </div>
            <div className="dashboard-column">
              <MapVisualization />
            </div>
          </div>
        </RosConnection>
      )}
        </>
      )}
    </div>
  );
}

export default App;
