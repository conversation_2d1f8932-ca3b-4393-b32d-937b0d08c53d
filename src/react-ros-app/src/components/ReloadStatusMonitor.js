import React, { useState, useEffect, useContext } from 'react';
import { RosContext } from './RosConnection';
import './ReloadStatusMonitor.css';

const ReloadStatusMonitor = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [geofencingStatus, setGeofencingStatus] = useState(null);
  const [rowCropStatus, setRowCropStatus] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    if (!isConnected || !ros) return;

    // Subscribe to geofencing node reload status
    const geofencingStatusTopic = new window.ROSLIB.Topic({
      ros: ros,
      name: '/geofencing_node/reload_status',
      messageType: 'weednix_sensors/ReloadStatus'
    });

    geofencingStatusTopic.subscribe((message) => {
      console.log('Geofencing status received:', message);
      setGeofencingStatus(message);
      setLastUpdate(new Date());
    });

    // Subscribe to row crop follower reload status
    const rowCropStatusTopic = new window.ROSLIB.Topic({
      ros: ros,
      name: '/row_crop_follower/reload_status',
      messageType: 'visual_servoing/ParameterReloadStatus'
    });

    rowCropStatusTopic.subscribe((message) => {
      console.log('Row crop follower status received:', message);
      setRowCropStatus(message);
      setLastUpdate(new Date());
    });

    // Cleanup subscriptions
    return () => {
      geofencingStatusTopic.unsubscribe();
      rowCropStatusTopic.unsubscribe();
    };
  }, [ros, isConnected]);

  const formatTimestamp = (rosTime) => {
    if (!rosTime) return 'Unknown';
    // Convert ROS time to JavaScript Date
    const timestamp = new Date((rosTime.secs * 1000) + (rosTime.nsecs / 1000000));
    return timestamp.toLocaleString();
  };

  const getStatusIcon = (success) => {
    return success ? '✅' : '❌';
  };

  const getStatusColor = (success) => {
    return success ? '#4CAF50' : '#f44336';
  };

  if (!isConnected) {
    return (
      <div className="reload-status-monitor">
        <h3>🔄 Parameter Reload Status</h3>
        <div className="status-disconnected">
          <p>⚠️ Not connected to ROS</p>
        </div>
      </div>
    );
  }

  return (
    <div className="reload-status-monitor">
      <h3>🔄 Parameter Reload Status</h3>
      
      {lastUpdate && (
        <div className="last-update">
          Last update: {lastUpdate.toLocaleTimeString()}
        </div>
      )}

      <div className="status-grid">
        {/* Geofencing Node Status */}
        <div className="status-card">
          <h4>🗺️ Geofencing Node</h4>
          {geofencingStatus ? (
            <div className="status-content">
              <div className="status-header">
                <span className="status-icon" style={{ color: getStatusColor(geofencingStatus.reload_success) }}>
                  {getStatusIcon(geofencingStatus.reload_success)}
                </span>
                <span className="status-text">
                  {geofencingStatus.reload_success ? 'Active' : 'Error'}
                </span>
              </div>
              
              <div className="status-details">
                <div className="detail-row">
                  <strong>Config File:</strong>
                  <span>{geofencingStatus.config_file_name || 'Unknown'}</span>
                </div>
                <div className="detail-row">
                  <strong>Last Reload:</strong>
                  <span>{formatTimestamp(geofencingStatus.last_reload_time)}</span>
                </div>
                <div className="detail-row">
                  <strong>Boundary Points:</strong>
                  <span>{geofencingStatus.boundary_points_count}</span>
                </div>
                <div className="detail-row">
                  <strong>Area:</strong>
                  <span>{geofencingStatus.boundary_area_hectares?.toFixed(2)} hectares</span>
                </div>
                <div className="detail-row">
                  <strong>Status:</strong>
                  <span>{geofencingStatus.reload_message}</span>
                </div>
                {geofencingStatus.error_message && (
                  <div className="detail-row error">
                    <strong>Error:</strong>
                    <span>{geofencingStatus.error_message}</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="status-waiting">
              <p>⏳ Waiting for status...</p>
            </div>
          )}
        </div>

        {/* Row Crop Follower Status */}
        <div className="status-card">
          <h4>🌾 Row Crop Follower</h4>
          {rowCropStatus ? (
            <div className="status-content">
              <div className="status-header">
                <span className="status-icon" style={{ color: getStatusColor(rowCropStatus.reload_success) }}>
                  {getStatusIcon(rowCropStatus.reload_success)}
                </span>
                <span className="status-text">
                  {rowCropStatus.reload_success ? 'Active' : 'Error'}
                </span>
              </div>
              
              <div className="status-details">
                <div className="detail-row">
                  <strong>Config File:</strong>
                  <span>{rowCropStatus.config_file_name || 'Unknown'}</span>
                </div>
                <div className="detail-row">
                  <strong>Last Reload:</strong>
                  <span>{formatTimestamp(rowCropStatus.last_reload_time)}</span>
                </div>
                <div className="detail-row">
                  <strong>Row Length:</strong>
                  <span>{rowCropStatus.row_length}m</span>
                </div>
                <div className="detail-row">
                  <strong>Row Spacing:</strong>
                  <span>{rowCropStatus.row_spacing}m</span>
                </div>
                <div className="detail-row">
                  <strong>Field Direction:</strong>
                  <span>{rowCropStatus.field_direction}</span>
                </div>
                <div className="detail-row">
                  <strong>Speed:</strong>
                  <span>{rowCropStatus.linear_speed}m/s</span>
                </div>
                <div className="detail-row">
                  <strong>Status:</strong>
                  <span>{rowCropStatus.reload_message}</span>
                </div>
                {rowCropStatus.error_message && (
                  <div className="detail-row error">
                    <strong>Error:</strong>
                    <span>{rowCropStatus.error_message}</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="status-waiting">
              <p>⏳ Waiting for status...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReloadStatusMonitor;
