import React, { useState, useContext, useEffect } from 'react';
import { RosContext } from './RosConnection';
import ROSLIB from 'roslib';
import './HardwareControl.css';

const HardwareControl = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [fanEnabled, setFanEnabled] = useState(false);
  const [lightEnabled, setLightEnabled] = useState(false);
  const [fanPublisher, setFanPublisher] = useState(null);
  const [lightPublisher, setLightPublisher] = useState(null);

  const [lastLogMessage, setLastLogMessage] = useState('');

  // Initialize ROS publishers and subscribers
  useEffect(() => {
    if (!isConnected || !ros) {
      setFanPublisher(null);
      setLightPublisher(null);
      return;
    }

    // Create publisher for fan control
    const fanPub = new ROSLIB.Topic({
      ros: ros,
      name: '/robot/fan_control',
      messageType: 'std_msgs/Bool'
    });

    // Create publisher for light control
    const lightPub = new ROSLIB.Topic({
      ros: ros,
      name: '/robot/light_control',
      messageType: 'std_msgs/Bool'
    });

    // Create subscriber for fan status
    const fanStatusSub = new ROSLIB.Topic({
      ros: ros,
      name: '/robot/fan_status',
      messageType: 'std_msgs/Bool'
    });

    // Create subscriber for light status
    const lightStatusSub = new ROSLIB.Topic({
      ros: ros,
      name: '/robot/light_status',
      messageType: 'std_msgs/Bool'
    });

    // Create subscriber for hardware log
    const hardwareLogSub = new ROSLIB.Topic({
      ros: ros,
      name: '/robot/hardware_log',
      messageType: 'std_msgs/String'
    });

    // Subscribe to status updates
    fanStatusSub.subscribe((message) => {
      setFanEnabled(message.data);
    });

    lightStatusSub.subscribe((message) => {
      setLightEnabled(message.data);
    });

    hardwareLogSub.subscribe((message) => {
      setLastLogMessage(message.data);
      console.log('Hardware log:', message.data);
    });

    setFanPublisher(fanPub);
    setLightPublisher(lightPub);

    // Cleanup on unmount
    return () => {
      if (fanPub) fanPub.unadvertise();
      if (lightPub) lightPub.unadvertise();
      if (fanStatusSub) fanStatusSub.unsubscribe();
      if (lightStatusSub) lightStatusSub.unsubscribe();
      if (hardwareLogSub) hardwareLogSub.unsubscribe();
    };
  }, [ros, isConnected]);

  const handleFanToggle = () => {
    if (!fanPublisher || !isConnected) return;

    const newState = !fanEnabled;

    const message = new ROSLIB.Message({
      data: newState
    });

    fanPublisher.publish(message);
    console.log(`Fan ${newState ? 'enabled' : 'disabled'}`);
  };

  const handleLightToggle = () => {
    if (!lightPublisher || !isConnected) return;

    const newState = !lightEnabled;

    const message = new ROSLIB.Message({
      data: newState
    });

    lightPublisher.publish(message);
    console.log(`Light ${newState ? 'enabled' : 'disabled'}`);
  };

  return (
    <div className="hardware-control">
      <h2>Hardware Control</h2>
      
      <div className="control-grid">
        {/* Fan Control */}
        <div className="control-item">
          <div className="control-header">
            <span className="control-icon">🌀</span>
            <span className="control-label">Fan</span>
          </div>
          <div className="toggle-container">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={fanEnabled}
                onChange={handleFanToggle}
                disabled={!isConnected}
              />
              <span className="toggle-slider"></span>
            </label>
            <span className={`status-text ${fanEnabled ? 'enabled' : 'disabled'}`}>
              {fanEnabled ? 'ON' : 'OFF'}
            </span>
          </div>
        </div>

        {/* Light Control */}
        <div className="control-item">
          <div className="control-header">
            <span className="control-icon">💡</span>
            <span className="control-label">Light</span>
          </div>
          <div className="toggle-container">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={lightEnabled}
                onChange={handleLightToggle}
                disabled={!isConnected}
              />
              <span className="toggle-slider"></span>
            </label>
            <span className={`status-text ${lightEnabled ? 'enabled' : 'disabled'}`}>
              {lightEnabled ? 'ON' : 'OFF'}
            </span>
          </div>
        </div>
      </div>

      {!isConnected && (
        <div className="connection-warning">
          <p>⚠️ Hardware controls disabled - No ROS connection</p>
        </div>
      )}

      {lastLogMessage && isConnected && (
        <div className="hardware-log">
          <h3>Latest Activity</h3>
          <p>{lastLogMessage}</p>
        </div>
      )}
    </div>
  );
};

export default HardwareControl;
