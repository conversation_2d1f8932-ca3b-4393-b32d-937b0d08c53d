# Parameter Reloading Implementation Guide

## Overview

This implementation provides dynamic parameter reloading for your ROS-based agricultural robot project, allowing configuration changes from the web interface to be applied to running nodes without requiring restarts.

## Features Implemented

### 1. Geofencing Node (`weednix_sensors/scripts/geofencing_node.py`)
- **Automatic File Monitoring**: Detects changes to GeoJSON boundary files every 5 seconds
- **Service-Based Reload**: `/geofencing_node/reload_boundary` service for manual triggering
- **Parameter Updates**: Supports changing the `geojson_file` parameter dynamically
- **Status Publishing**: `/geofencing_node/reload_status` topic with boundary information

### 2. Row Crop Follower (`visual_servoing/scripts/row_crop_follower.py`)
- **Automatic File Monitoring**: Detects changes to YAML config files every 5 seconds
- **Service-Based Reload**: `/row_crop_follower/reload_parameters` service for manual triggering
- **Complete Parameter Reload**: Reloads all parameters including field, movement, and vision settings
- **Tracker Updates**: Automatically updates neighbourhood tracker when dimensions change
- **Status Publishing**: `/row_crop_follower/reload_status` topic with parameter values

### 3. Web Interface Integration
- **Automatic Triggering**: Parameter/boundary reloads triggered when saving from web interface
- **User Feedback**: Success/failure notifications for reload operations
- **API Endpoints**: Server-side ROS communication for triggering reloads
- **Real-time Status Display**: Live monitoring of parameter reload status in web interface

### 4. Status Monitoring System
- **Custom Message Types**: `ReloadStatus.msg` and `ParameterReloadStatus.msg`
- **Periodic Publishing**: Status published every 30 seconds and after each reload
- **Comprehensive Information**: Includes timestamps, success flags, error messages, and parameter values
- **Web Dashboard**: Real-time status display in React interface

## Architecture

```
Web Interface (React)
    ↓ (HTTP API calls)
Node.js Server (with ROSLIB)
    ↓ (ROS Services)
ROS Nodes (Python)
    ↓ (File monitoring + Service handlers)
Configuration Files (YAML/GeoJSON)
```

## Usage Instructions

### From Web Interface
1. **Map Changes**: Select or create a new map in the Map Setup page
   - Boundary changes automatically trigger geofencing node reload
   - Success/failure notifications displayed to user

2. **Parameter Changes**: Modify parameters in Mission Parameters form
   - Parameter changes automatically trigger row crop follower reload
   - All parameters updated without node restart

### Manual Testing
Use the provided test script:
```bash
cd /home/<USER>/weednix_ws/src
python3 test_parameter_reload.py
```

### Service Calls (Advanced)
```bash
# Reload geofencing boundary
rosservice call /geofencing_node/reload_boundary

# Reload row crop follower parameters  
rosservice call /row_crop_follower/reload_parameters
```

## File Structure

### Modified Files
- `weednix_sensors/scripts/geofencing_node.py` - Added reload functionality
- `visual_servoing/scripts/row_crop_follower.py` - Added reload functionality
- `react-ros-app/src/components/MissionParametersForm.js` - Added reload triggering
- `react-ros-app/src/components/MapSetupPage.js` - Added reload triggering
- `react-ros-app/server.js` - Added ROS communication endpoints

### New Files
- `test_parameter_reload.py` - Test script for verifying functionality
- `PARAMETER_RELOADING_GUIDE.md` - This documentation

## API Endpoints

### POST /api/reload-parameters
Triggers parameter reload for row crop follower
```json
{
  "configFile": "new_config_file.yaml"
}
```

### POST /api/reload-boundary
Triggers boundary reload for geofencing node
```json
{
  "geoJsonFile": "new_boundary.geojson"
}
```

## Error Handling

### Node-Level
- File monitoring errors logged but don't crash nodes
- Service call failures return descriptive error messages
- Invalid parameter values handled gracefully

### Web Interface
- Network errors displayed to user with fallback options
- Partial failures (save success, reload failure) clearly communicated
- Timeout handling for ROS service calls

## Benefits

1. **No Downtime**: Configuration changes applied without stopping robot operations
2. **Real-time Updates**: Changes from web interface immediately active
3. **Automatic Detection**: File changes detected and applied automatically
4. **User Friendly**: Clear feedback on success/failure of operations
5. **Robust**: Comprehensive error handling and fallback mechanisms

## Troubleshooting

### Common Issues
1. **Service Not Available**: Ensure nodes are running before testing
2. **File Permission Errors**: Check write permissions on config directories
3. **ROS Connection Failures**: Verify rosbridge is running on port 9090

### Debug Commands
```bash
# Check if services are available
rosservice list | grep reload

# Monitor parameter changes
rostopic echo /rosout | grep -i reload

# Test ROS connection
rostopic list
```

## Future Enhancements

1. **Parameter Validation**: Add validation before applying new parameters
2. **Rollback Capability**: Ability to revert to previous configuration
3. **Configuration History**: Track and display parameter change history
4. **Real-time Monitoring**: Live parameter monitoring in web interface
5. **Batch Updates**: Apply multiple parameter changes atomically
