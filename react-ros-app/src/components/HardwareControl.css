.hardware-control {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 10px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.hardware-control h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.4em;
  font-weight: 600;
  text-align: center;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 15px;
}

.control-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.control-item:hover {
  background: #f1f3f4;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.control-icon {
  font-size: 1.5em;
  margin-right: 8px;
}

.control-label {
  font-weight: 600;
  color: #333;
  font-size: 1.1em;
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: #4CAF50;
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px #4CAF50;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

input:disabled + .toggle-slider {
  background-color: #ddd;
  cursor: not-allowed;
}

input:disabled + .toggle-slider:before {
  background-color: #f5f5f5;
}

.status-text {
  font-weight: 600;
  font-size: 0.9em;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 30px;
  text-align: center;
}

.status-text.enabled {
  color: #4CAF50;
  background-color: #e8f5e8;
}

.status-text.disabled {
  color: #666;
  background-color: #f0f0f0;
}

.connection-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 10px;
  margin-top: 15px;
  text-align: center;
}

.connection-warning p {
  margin: 0;
  color: #856404;
  font-size: 0.9em;
}

.hardware-log {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-top: 15px;
}

.hardware-log h3 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 1em;
  font-weight: 600;
}

.hardware-log p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9em;
  font-family: 'Courier New', monospace;
  background: white;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .control-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .hardware-control {
    padding: 15px;
  }
  
  .control-item {
    padding: 12px;
  }
  
  .toggle-container {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .status-text {
    align-self: flex-end;
  }
}
