set(_CATKIN_CURRENT_PACKAGE "weednix_sensors")
set(weednix_sensors_VERSION "0.0.0")
set(weednix_sensors_MAINTAINER "abdulaziz <<EMAIL>>")
set(weednix_sensors_PACKAGE_FORMAT "2")
set(weednix_sensors_BUILD_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "message_generation")
set(weednix_sensors_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs")
set(weednix_sensors_BUILDTOOL_DEPENDS "catkin")
set(weednix_sensors_BUILDTOOL_EXPORT_DEPENDS )
set(weednix_sensors_EXEC_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "message_runtime")
set(weednix_sensors_RUN_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "message_runtime")
set(weednix_sensors_TEST_DEPENDS )
set(weednix_sensors_DOC_DEPENDS )
set(weednix_sensors_URL_WEBSITE "")
set(weednix_sensors_URL_BUGTRACKER "")
set(weednix_sensors_URL_REPOSITORY "")
set(weednix_sensors_DEPRECATED "")