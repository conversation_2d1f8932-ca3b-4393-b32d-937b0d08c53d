# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/weednix_ws/src/visual_servoing/msg/ParameterReloadStatus.msg"
services_str = ""
pkg_name = "visual_servoing"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "visual_servoing;/home/<USER>/weednix_ws/src/visual_servoing/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = 'TRUE' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
