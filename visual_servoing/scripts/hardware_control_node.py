#!/usr/bin/env python3

import rospy
from std_msgs.msg import Bool, String
import time

class HardwareControlNode:
    def __init__(self):
        rospy.init_node('hardware_control_node', anonymous=True)
        
        # Initialize hardware states
        self.fan_enabled = False
        self.light_enabled = False
        
        # Subscribers for hardware control
        self.fan_sub = rospy.Subscriber('/robot/fan_control', Bo<PERSON>, self.fan_callback)
        self.light_sub = rospy.Subscriber('/robot/light_control', Bool, self.light_callback)
        
        # Publishers for hardware status
        self.fan_status_pub = rospy.Publisher('/robot/fan_status', Bool, queue_size=1)
        self.light_status_pub = rospy.Publisher('/robot/light_status', Bool, queue_size=1)
        self.hardware_log_pub = rospy.Publisher('/robot/hardware_log', String, queue_size=10)
        
        # Timer for periodic status publishing
        self.status_timer = rospy.Timer(rospy.Duration(5.0), self.publish_status)
        
        rospy.loginfo("Hardware Control Node initialized")
        rospy.loginfo("Subscribed to:")
        rospy.loginfo("  - /robot/fan_control")
        rospy.loginfo("  - /robot/light_control")
        rospy.loginfo("Publishing to:")
        rospy.loginfo("  - /robot/fan_status")
        rospy.loginfo("  - /robot/light_status")
        rospy.loginfo("  - /robot/hardware_log")
        
        # Publish initial status
        self.publish_status()
        
    def fan_callback(self, msg):
        """Handle fan control messages"""
        old_state = self.fan_enabled
        self.fan_enabled = msg.data
        
        if old_state != self.fan_enabled:
            state_str = "ON" if self.fan_enabled else "OFF"
            log_msg = f"Fan turned {state_str} at {time.strftime('%H:%M:%S')}"
            rospy.loginfo(log_msg)
            
            # Publish log message
            log_ros_msg = String()
            log_ros_msg.data = log_msg
            self.hardware_log_pub.publish(log_ros_msg)
            
            # Publish status immediately
            self.publish_fan_status()
            
            # Here you would add actual hardware control code
            # For example:
            # if self.fan_enabled:
            #     self.turn_on_fan()
            # else:
            #     self.turn_off_fan()
            
    def light_callback(self, msg):
        """Handle light control messages"""
        old_state = self.light_enabled
        self.light_enabled = msg.data
        
        if old_state != self.light_enabled:
            state_str = "ON" if self.light_enabled else "OFF"
            log_msg = f"Light turned {state_str} at {time.strftime('%H:%M:%S')}"
            rospy.loginfo(log_msg)
            
            # Publish log message
            log_ros_msg = String()
            log_ros_msg.data = log_msg
            self.hardware_log_pub.publish(log_ros_msg)
            
            # Publish status immediately
            self.publish_light_status()
            
            # Here you would add actual hardware control code
            # For example:
            # if self.light_enabled:
            #     self.turn_on_light()
            # else:
            #     self.turn_off_light()
    
    def publish_fan_status(self):
        """Publish current fan status"""
        status_msg = Bool()
        status_msg.data = self.fan_enabled
        self.fan_status_pub.publish(status_msg)
        
    def publish_light_status(self):
        """Publish current light status"""
        status_msg = Bool()
        status_msg.data = self.light_enabled
        self.light_status_pub.publish(status_msg)
        
    def publish_status(self, event=None):
        """Publish status of all hardware components"""
        self.publish_fan_status()
        self.publish_light_status()
        
        # Publish summary log
        status_summary = f"Hardware Status - Fan: {'ON' if self.fan_enabled else 'OFF'}, Light: {'ON' if self.light_enabled else 'OFF'}"
        log_msg = String()
        log_msg.data = status_summary
        self.hardware_log_pub.publish(log_msg)
        
    # Placeholder methods for actual hardware control
    # These would be implemented based on your specific hardware setup
    
    def turn_on_fan(self):
        """Turn on the fan - implement based on your hardware"""
        # Example: GPIO control, serial communication, etc.
        pass
        
    def turn_off_fan(self):
        """Turn off the fan - implement based on your hardware"""
        # Example: GPIO control, serial communication, etc.
        pass
        
    def turn_on_light(self):
        """Turn on the light - implement based on your hardware"""
        # Example: GPIO control, serial communication, etc.
        pass
        
    def turn_off_light(self):
        """Turn off the light - implement based on your hardware"""
        # Example: GPIO control, serial communication, etc.
        pass

if __name__ == '__main__':
    try:
        hardware_node = HardwareControlNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        rospy.loginfo("Hardware Control Node shutting down")
        pass
