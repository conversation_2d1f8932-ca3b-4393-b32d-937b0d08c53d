.mission-parameters-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 1rem;
}

.parameters-header {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.parameters-header h1 {
  margin: 0;
  color: #333;
  flex-grow: 1;
}

.parameters-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.parameters-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.parameters-form {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
}

.form-group input.error {
  border-color: #dc3545;
}

.form-group small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.85rem;
}

.error-text {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

.mission-summary {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.mission-summary h3 {
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.summary-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 5px;
  border-left: 4px solid #007bff;
}

.summary-item strong {
  color: #333;
}

.summary-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.summary-section h4 {
  color: #333;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.debug-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.debug-summary span {
  background: #f8f9fa;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
  border-left: 3px solid #17a2b8;
  color: #333;
}

.safety-notice {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.safety-notice h4 {
  color: #856404;
  margin-bottom: 1rem;
}

.safety-notice ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #856404;
}

.safety-notice li {
  margin-bottom: 0.5rem;
}

.parameters-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.back-btn:hover {
  background: #5a6268;
}

.launch-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.launch-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.launch-btn:active {
  transform: translateY(0);
}

@media (max-width: 1024px) {
  .parameters-container {
    grid-template-columns: 1fr;
  }
  
  .mission-summary {
    position: static;
  }
}

@media (max-width: 768px) {
  .parameters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .parameters-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .launch-btn {
    width: 100%;
  }
}
