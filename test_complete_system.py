#!/usr/bin/env python3

"""
Comprehensive test script for the fixed parameter reloading system.
Tests all three critical issues that were fixed:
1. Existing map selection triggering boundary reload
2. New map creation boundary reload success
3. Unified status system (no duplicate implementations)
"""

import rospy
import json
import time
from std_msgs.msg import String
from std_srvs.srv import Trigger

class SystemTester:
    def __init__(self):
        rospy.init_node('system_tester', anonymous=True)
        self.status_received = False
        self.last_status = None
        
        # Subscribe to unified status topic
        self.status_sub = rospy.Subscriber('/system/reload_status', String, self.status_callback)
        
        print("🧪 Comprehensive System Test")
        print("=" * 60)
        
    def status_callback(self, msg):
        """Callback for status messages"""
        try:
            self.last_status = json.loads(msg.data)
            self.status_received = True
            print(f"📊 Status Update: {self.last_status.get('message', 'No message')}")
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing status: {e}")

    def test_services_available(self):
        """Test 1: Check if reload services are available"""
        print("\n🔍 Test 1: Service Availability")
        print("-" * 40)
        
        services_to_check = [
            '/geofencing_node/reload_boundary',
            '/row_crop_follower/reload_parameters'
        ]
        
        all_available = True
        for service_name in services_to_check:
            try:
                rospy.wait_for_service(service_name, timeout=2.0)
                print(f"✅ {service_name} - Available")
            except rospy.ROSException:
                print(f"❌ {service_name} - Not available")
                all_available = False
        
        return all_available

    def test_geofencing_reload(self):
        """Test 2: Test geofencing boundary reload service"""
        print("\n🗺️  Test 2: Geofencing Boundary Reload")
        print("-" * 40)
        
        try:
            reload_service = rospy.ServiceProxy('/geofencing_node/reload_boundary', Trigger)
            response = reload_service()
            
            if response.success:
                print(f"✅ Boundary reload successful: {response.message}")
                return True
            else:
                print(f"❌ Boundary reload failed: {response.message}")
                return False
                
        except rospy.ServiceException as e:
            print(f"❌ Service call failed: {e}")
            return False

    def test_row_crop_reload(self):
        """Test 3: Test row crop follower parameter reload service"""
        print("\n🌾 Test 3: Row Crop Follower Parameter Reload")
        print("-" * 40)
        
        try:
            reload_service = rospy.ServiceProxy('/row_crop_follower/reload_parameters', Trigger)
            response = reload_service()
            
            if response.success:
                print(f"✅ Parameter reload successful: {response.message}")
                return True
            else:
                print(f"❌ Parameter reload failed: {response.message}")
                return False
                
        except rospy.ServiceException as e:
            print(f"❌ Service call failed: {e}")
            return False

    def test_unified_status_topic(self):
        """Test 4: Test unified status topic"""
        print("\n📡 Test 4: Unified Status Topic")
        print("-" * 40)
        
        # Wait for status message
        print("Waiting for status message (10 seconds)...")
        self.status_received = False
        
        timeout = rospy.Time.now() + rospy.Duration(10.0)
        while rospy.Time.now() < timeout and not rospy.is_shutdown():
            if self.status_received:
                break
            rospy.sleep(0.1)
        
        if self.status_received and self.last_status:
            print("✅ Status topic working")
            print(f"   Message: {self.last_status.get('message', 'No message')}")
            print(f"   Success: {self.last_status.get('success', 'Unknown')}")
            print(f"   Timestamp: {self.last_status.get('timestamp', 'Unknown')}")
            return True
        else:
            print("❌ No status message received")
            return False

    def test_no_custom_topics(self):
        """Test 5: Verify no custom message topics exist"""
        print("\n🧹 Test 5: No Custom Message Topics")
        print("-" * 40)
        
        # Get list of all topics
        topics = rospy.get_published_topics()
        topic_names = [topic[0] for topic in topics]
        
        # Check for custom message topics that should NOT exist
        forbidden_topics = [
            '/geofencing_node/reload_status',
            '/row_crop_follower/reload_status'
        ]
        
        found_forbidden = []
        for topic in forbidden_topics:
            if topic in topic_names:
                found_forbidden.append(topic)
        
        if found_forbidden:
            print(f"❌ Found forbidden custom topics: {found_forbidden}")
            return False
        else:
            print("✅ No custom message topics found (cleanup successful)")
            return True

    def test_unified_topic_exists(self):
        """Test 6: Verify unified topic exists"""
        print("\n📋 Test 6: Unified Topic Exists")
        print("-" * 40)
        
        topics = rospy.get_published_topics()
        topic_names = [topic[0] for topic in topics]
        
        if '/system/reload_status' in topic_names:
            print("✅ Unified status topic exists: /system/reload_status")
            return True
        else:
            print("❌ Unified status topic not found: /system/reload_status")
            return False

    def run_all_tests(self):
        """Run all tests and provide summary"""
        print("Starting comprehensive system tests...")
        print("Make sure the following are running:")
        print("- roscore")
        print("- geofencing_node.py")
        print("- row_crop_follower.py") 
        print("- system_status_publisher.py")
        print()
        
        # Wait a moment for everything to initialize
        rospy.sleep(2.0)
        
        # Run all tests
        test_results = {
            "Service Availability": self.test_services_available(),
            "Geofencing Reload": self.test_geofencing_reload(),
            "Row Crop Reload": self.test_row_crop_reload(),
            "Unified Status Topic": self.test_unified_status_topic(),
            "No Custom Topics": self.test_no_custom_topics(),
            "Unified Topic Exists": self.test_unified_topic_exists()
        }
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<25} {status}")
            if result:
                passed += 1
        
        print("-" * 60)
        print(f"Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! System is working correctly.")
            print("\n✅ Critical Issues Fixed:")
            print("   1. Existing map selection triggers boundary reload")
            print("   2. New map creation boundary reload works")
            print("   3. Duplicate status system cleaned up")
            return True
        else:
            print("⚠️  Some tests failed. Check the issues above.")
            return False

if __name__ == '__main__':
    try:
        tester = SystemTester()
        success = tester.run_all_tests()
        exit(0 if success else 1)
    except rospy.ROSInterruptException:
        print("\nTest interrupted by user")
        exit(1)
    except Exception as e:
        print(f"Test failed with error: {e}")
        exit(1)
