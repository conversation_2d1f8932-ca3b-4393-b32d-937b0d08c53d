# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "visual_crop_row: 1 messages, 0 services")

set(MSG_I_FLAGS "-Ivisual_crop_row:/home/<USER>/weednix_ws/src/visual-crop-row/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(visual_crop_row_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_custom_target(_visual_crop_row_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "visual_crop_row" "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" ""
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(visual_crop_row
  "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/visual_crop_row
)

### Generating Services

### Generating Module File
_generate_module_cpp(visual_crop_row
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/visual_crop_row
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(visual_crop_row_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(visual_crop_row_generate_messages visual_crop_row_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_dependencies(visual_crop_row_generate_messages_cpp _visual_crop_row_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(visual_crop_row_gencpp)
add_dependencies(visual_crop_row_gencpp visual_crop_row_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS visual_crop_row_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(visual_crop_row
  "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/visual_crop_row
)

### Generating Services

### Generating Module File
_generate_module_eus(visual_crop_row
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/visual_crop_row
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(visual_crop_row_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(visual_crop_row_generate_messages visual_crop_row_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_dependencies(visual_crop_row_generate_messages_eus _visual_crop_row_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(visual_crop_row_geneus)
add_dependencies(visual_crop_row_geneus visual_crop_row_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS visual_crop_row_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(visual_crop_row
  "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/visual_crop_row
)

### Generating Services

### Generating Module File
_generate_module_lisp(visual_crop_row
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/visual_crop_row
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(visual_crop_row_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(visual_crop_row_generate_messages visual_crop_row_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_dependencies(visual_crop_row_generate_messages_lisp _visual_crop_row_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(visual_crop_row_genlisp)
add_dependencies(visual_crop_row_genlisp visual_crop_row_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS visual_crop_row_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(visual_crop_row
  "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/visual_crop_row
)

### Generating Services

### Generating Module File
_generate_module_nodejs(visual_crop_row
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/visual_crop_row
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(visual_crop_row_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(visual_crop_row_generate_messages visual_crop_row_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_dependencies(visual_crop_row_generate_messages_nodejs _visual_crop_row_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(visual_crop_row_gennodejs)
add_dependencies(visual_crop_row_gennodejs visual_crop_row_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS visual_crop_row_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(visual_crop_row
  "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/visual_crop_row
)

### Generating Services

### Generating Module File
_generate_module_py(visual_crop_row
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/visual_crop_row
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(visual_crop_row_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(visual_crop_row_generate_messages visual_crop_row_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/weednix_ws/src/visual-crop-row/msg/vs_msg.msg" NAME_WE)
add_dependencies(visual_crop_row_generate_messages_py _visual_crop_row_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(visual_crop_row_genpy)
add_dependencies(visual_crop_row_genpy visual_crop_row_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS visual_crop_row_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/visual_crop_row)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/visual_crop_row
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(visual_crop_row_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/visual_crop_row)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/visual_crop_row
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(visual_crop_row_generate_messages_eus std_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/visual_crop_row)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/visual_crop_row
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(visual_crop_row_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/visual_crop_row)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/visual_crop_row
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(visual_crop_row_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/visual_crop_row)
  install(CODE "execute_process(COMMAND \"/usr/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/visual_crop_row\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/visual_crop_row
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(visual_crop_row_generate_messages_py std_msgs_generate_messages_py)
endif()
