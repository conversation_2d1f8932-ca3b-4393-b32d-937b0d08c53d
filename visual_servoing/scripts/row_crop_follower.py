import cv2
import numpy as np
import rospy
import os
from sensor_msgs.msg import Image
from geometry_msgs.msg import Twist
from std_msgs.msg import String, <PERSON><PERSON>, Header
from std_srvs.srv import Trigger, TriggerResponse
from visual_servoing.msg import ParameterReloadStatus
from cv_bridge import CvBridge
import tf
from nav_msgs.msg import Odometry
import rospkg  # Import the rospkg library
import yaml

class NeighbourhoodTracker:
    def __init__(self, image_width, image_height, initial_Xc, initial_Yc, width_L, height_H):
        self.image_width = image_width
        self.image_height = image_height
        self.initial_Xc = initial_Xc
        self.initial_Yc = initial_Yc
        self.width_L = width_L
        self.height_H = height_H

        # Current state of the window center
        self.current_Xc = float(self.initial_Xc)
        self.current_Yc = float(self.initial_Yc)

        # Results for the current frame (initialize empty)
        self.points_in_neighbourhood = np.empty((0, 2), dtype=np.float32)
        self.extreme_points = [None, None] # [top_point, bottom_point]

        print(f"NeighbourhoodTracker initialized.")
        print(f"  Initial Center: ({self.current_Xc}, {self.current_Yc})")
        print(f"  Size (LxH): ({self.width_L} x {self.height_H})")

    def reset(self):
        """Resets the window to its initial position."""
        self.current_Xc = float(self.initial_Xc)
        self.current_Yc = float(self.initial_Yc)
        self.points_in_neighbourhood = np.empty((0, 2), dtype=np.float32)
        self.extreme_points = [None, None]
        print("*********** Neighbourhood Reset ***********")
        print(f"  Center set to: ({self.current_Xc}, {self.current_Yc})")


    def filter_points(self, all_detected_points):
        """
        Filters the input points to keep only those inside the current neighbourhood window.

        Args:
            all_detected_points (np.ndarray): An (N, 2) NumPy array of detected points [x, y].
                                             Assumed dtype is float32 or float64.
        """
        if not isinstance(all_detected_points, np.ndarray) or all_detected_points.ndim != 2 or all_detected_points.shape[1] != 2:
             # Handle empty or invalid input gracefully
             self.points_in_neighbourhood = np.empty((0, 2), dtype=np.float32)
             # print("Warning: Invalid input to filter_points.")
             return

        half_L = self.width_L / 2.0
        half_H = self.height_H / 2.0
        min_X = self.current_Xc - half_L
        max_X = self.current_Xc + half_L
        min_Y = self.current_Yc - half_H
        max_Y = self.current_Yc + half_H

        if all_detected_points.shape[0] > 0:
            x_coords = all_detected_points[:, 0]
            y_coords = all_detected_points[:, 1]

            mask_x = (x_coords > min_X) & (x_coords < max_X)
            mask_y = (y_coords > min_Y) & (y_coords < max_Y)

            combined_mask = mask_x & mask_y

            self.points_in_neighbourhood = all_detected_points[combined_mask]
        else:
            self.points_in_neighbourhood = np.empty((0, 2), dtype=all_detected_points.dtype)

        # print(f"Points in neighbourhood: {self.points_in_neighbourhood.shape[0]}")

    def find_best_window_position(self, all_detected_points, step_size=20):
        """
        Finds the best window position (X, Y) that maximizes the density of
        detected points within the window.

        Args:
            all_detected_points (np.ndarray): (N, 2) NumPy array of detected points.
            step_size (int): The step size for sliding the window (in pixels).

        Returns:
            tuple: (best_x, best_y, max_density) - The X and Y coordinates of the
                   best window position and the maximum density found.
        """
        half_L = self.width_L / 2.0
        half_H = self.height_H / 2.0

        # Define the search space
        x_start = int(half_L)
        x_end = int(self.image_width - half_L)
        y_start = int(half_H)
        y_end = int(self.image_height - half_H)

        max_density = 0
        best_x = self.current_Xc  # Initialize with current position
        best_y = self.current_Yc

        # Iterate over all possible window positions
        for x in range(x_start, x_end, step_size):
            for y in range(y_start, y_end, step_size):
                # Calculate the window boundaries
                min_x = x - half_L
                max_x = x + half_L
                min_y = y - half_H
                max_y = y + half_H

                # Count the number of points within the window
                x_coords = all_detected_points[:, 0]
                y_coords = all_detected_points[:, 1]

                mask_x = (x_coords >= min_x) & (x_coords <= max_x)
                mask_y = (y_coords >= min_y) & (y_coords <= max_y)

                combined_mask = mask_x & mask_y
                num_points_in_window = np.sum(combined_mask)

                # Calculate the density of points within the window
                density = num_points_in_window / (self.width_L * self.height_H)

                # Update the best window position if the current density is higher
                if density > max_density:
                    max_density = density
                    best_x = x
                    best_y = y

        return best_x, best_y, max_density


    def update_position_for_next_frame(self, all_detected_points):
        """
        Updates the window's horizontal (current_Xc) and vertical (current_Yc) center
        for the next frame, positioning the window over the area with the highest
        density of detected points.

        Args:
            all_detected_points (np.ndarray): (N, 2) NumPy array from the current frame.
        """
        if not isinstance(all_detected_points, np.ndarray) or all_detected_points.ndim != 2 or all_detected_points.shape[1] != 2:
            # print("Warning: Invalid input to update_position_for_next_frame.")
            return  # Do not update if input is invalid

        if all_detected_points.shape[0] > 5: # Require a *minimum* number of points
            # Calculate the window boundaries
            half_L = self.width_L / 2.0
            half_H = self.height_H / 2.0

            # Calculate the density of points within the window at different positions
            best_x, best_y, max_density = self.find_best_window_position(all_detected_points)

            # Update the window position to the position with the highest density
            self.current_Xc = float(best_x)
            self.current_Yc = float(best_y)

        else:
            # No points detected: Move towards the center of the image
            image_center_y = self.image_height / 2.0
            # Adjust the Y position *gradually* towards the center
            self.current_Yc += 0.1 * (image_center_y - self.current_Yc)  # Adjust 0.1 for speed
            self.current_Xc = self.image_width / 2.0 #move to the center

        # Optional: Clamp the center X and Y to keep the window within bounds
        min_center_x = self.width_L / 2.0
        max_center_x = self.image_width - (self.width_L / 2.0)
        self.current_Xc = np.clip(self.current_Xc, min_center_x, max_center_x)

        min_center_y = self.height_H / 2.0
        max_center_y = self.image_height - (self.height_H / 2.0)
        self.current_Yc = np.clip(self.current_Yc, min_center_y, max_center_y)


    def draw(self, image, color=(255, 204, 102), thickness=2):
        """
        Draws the current neighbourhood window onto the provided image.

        Args:
            image (np.ndarray): The image (NumPy array) to draw on.
            color (tuple): BGR color tuple.
            thickness (int): Line thickness.
        """
        if image is None:
            return

        half_L = self.width_L / 2.0
        half_H = self.height_H / 2.0

        # Calculate top-left corner (ensure integer coordinates for drawing)
        top_left_x = int(self.current_Xc - half_L)
        top_left_y = int(self.current_Yc - half_H)

        # Calculate bottom-right corner
        bottom_right_x = int(top_left_x + self.width_L)
        bottom_right_y = int(top_left_y + self.height_H)

        # Define points for cv2.rectangle
        pt1 = (top_left_x, top_left_y)
        pt2 = (bottom_right_x, bottom_right_y)

        # Draw the rectangle
        cv2.rectangle(image, pt1, pt2, color, thickness)

        # Optional: Draw the center point
        # center_pt = (int(self.current_Xc), int(self.current_Yc))
        # cv2.circle(image, center_pt, 3, (0, 0, 255), -1) # Red dot

    def compute_extreme_points(self):
        """
        Finds the points with min and max Y coordinates within the neighbourhood.
        Stores results in self.extreme_points: [top_point, bottom_point].
        Points are (x, y) NumPy arrays or None if no points are inside.
        """
        self.extreme_points = [None, None] # Reset
        points_inside = self.points_in_neighbourhood

        if points_inside.shape[0] > 0:
            y_coords_inside = points_inside[:, 1]
            min_y_idx = np.argmin(y_coords_inside)
            max_y_idx = np.argmax(y_coords_inside)

            # Store the actual points (shape (2,))
            self.extreme_points[0] = points_inside[min_y_idx] # Top-most point
            self.extreme_points[1] = points_inside[max_y_idx] # Bottom-most point
        # else: self.extreme_points remains [None, None]


# this is change

class RowCropFollower:
    def __init__(self):
        rospy.init_node('row_crop_follower', anonymous=True)
        self.bridge = CvBridge()

        # Get image topic from parameter
        self.image_topic = rospy.get_param('row_crop_follower/image_topic', '/camera_sim/color/image_raw')
        rospy.loginfo(f"Using image topic: {self.image_topic}")

        self.image_sub = rospy.Subscriber(self.image_topic, Image, self.image_callback)
        self.cmd_pub = rospy.Publisher("/cmd_vel", Twist, queue_size=10)
        self.rate = rospy.Rate(10)  # 10 Hz loop rate

        # Load parameters from YAML file
        # Get the path to the package
        rospack = rospkg.RosPack()
        package_path = rospack.get_path('visual_servoing')

        # Get config file name from parameter (default to row_crop_config.yaml)
        config_file_name = rospy.get_param('~config_file', 'row_crop_config.yaml')
        config_file_path = package_path + '/config/' + config_file_name

        # Load parameters from the YAML file
        rospy.loginfo("Loading parameters from %s", config_file_path)

        # Store existing launch file parameters before loading YAML
        existing_params = {}
        try:
            # Get all existing parameters under row_crop_follower namespace
            all_params = rospy.get_param('row_crop_follower', {})
            existing_params = all_params.copy()
            rospy.loginfo("Found existing launch file parameters: %s", list(existing_params.keys()))
        except:
            rospy.loginfo("No existing parameters found")

        # Load YAML parameters
        yaml_params = self.load_yaml(config_file_path)
        if yaml_params:
            rospy.set_param('row_crop_follower', yaml_params)
            rospy.loginfo("Loaded YAML parameters: %s", list(yaml_params.keys()))

        # Override YAML parameters with launch file parameters
        for param_name, param_value in existing_params.items():
            full_param_name = 'row_crop_follower/' + param_name
            rospy.set_param(full_param_name, param_value)
            rospy.loginfo("Override parameter %s with launch file value: %s", param_name, param_value)

        self.row_length = rospy.get_param('row_crop_follower/row_length')
        self.row_spacing = rospy.get_param('row_crop_follower/row_spacing')
        self.field_direction = rospy.get_param('row_crop_follower/field_direction')
        self.end_of_row_threshold = rospy.get_param('row_crop_follower/end_of_row_threshold')
        self.wrong_distance_threshold = rospy.get_param('row_crop_follower/wrong_distance_threshold') # Load the new parameter
        self.use_odometry = rospy.get_param('row_crop_follower/use_odometry') # Load the use_odometry parameter
        self.use_feedback = rospy.get_param('row_crop_follower/use_feedback') # Load the use_feedback parameter
        self.following_only = rospy.get_param('row_crop_follower/following_only', False) # Parameter to disable all states except "following"
        self.show_frames = rospy.get_param('row_crop_follower/show_frames', True) # Parameter to control frame display
        self.tuning_mode = rospy.get_param('row_crop_follower/tuning_mode', False) # Parameter to enable tuning mode with HSV and contour windows

        self.neighbourhood_width = rospy.get_param('row_crop_follower/neighbourhood_width')
        self.neighbourhood_height = rospy.get_param('row_crop_follower/neighbourhood_height')
        self.initial_x = rospy.get_param('row_crop_follower/initial_x')
        self.initial_y = rospy.get_param('row_crop_follower/initial_y')

        self.h_min = rospy.get_param('row_crop_follower/h_min')
        self.h_max = rospy.get_param('row_crop_follower/h_max')
        self.s_min = rospy.get_param('row_crop_follower/s_min')
        self.s_max = rospy.get_param('row_crop_follower/s_max')
        self.v_min = rospy.get_param('row_crop_follower/v_min')
        self.v_max = rospy.get_param('row_crop_follower/v_max')
        self.point_spacing = rospy.get_param('row_crop_follower/point_spacing')
        self.kp = rospy.get_param('row_crop_follower/kp')

        self.A = rospy.get_param('row_crop_follower/A')
        theta_degrees = rospy.get_param('row_crop_follower/theta_degrees')
        self.theta = np.radians(theta_degrees) # Convert to radians
        self.linear_speed = rospy.get_param('row_crop_follower/linear_speed')
        self.angular_speed = rospy.get_param('row_crop_follower/angular_speed')

        self.R = (self.row_spacing * np.sin(180 - (2 * self.theta)) / np.sin(self.theta)+0.25)


        # Neighbourhood Tracker Parameters (adjust these!)
        self.tracker = NeighbourhoodTracker(640, 480, self.initial_x, self.initial_y, self.neighbourhood_width, self.neighbourhood_height) # Image width and height

        # State machine
        self.state = "following"  # "following", "turning", "exiting", "diagonal1", "rotating1", "diagonal2", "rotating2", "entering"

        # Odometry data
        self.current_x = 0.0
        self.current_y = 0.0
        self.current_yaw = 0.0 # in radians

        self.initial_x = 0.0
        self.initial_y = 0.0
        self.initial_yaw = 0.0

        self.odom_sub = rospy.Subscriber("/odometry/filtered", Odometry, self.odom_callback) #subscribe to the odometry topic

        # Mode control for autonomous/manual switching
        self.autonomous_mode = False  # Default to autonomous mode
        self.mode_sub = rospy.Subscriber("/robot_mode", String, self.mode_callback)
        self.status_pub = rospy.Publisher("/visual_servoing/status", Bool, queue_size=1)
        self.reload_status_pub = rospy.Publisher('~reload_status', ParameterReloadStatus, queue_size=1)

        self.no_green_frames = 0 # Counter for consecutive frames with low green pixels
        self.no_green_threshold = 3 # Number of consecutive frames required to trigger turn

        # NEW: Row Counter and Direction
        self.row_count = 1  # Start at row 1
        self.row_direction = 1  # 1 for forward, -1 for backward

        # Parameter reloading functionality
        self.config_file_path = config_file_path
        self.last_config_modified_time = self.get_config_file_modified_time()
        self.last_reload_time = rospy.Time.now()

        # Service for reloading parameters
        self.reload_service = rospy.Service('~reload_parameters', Trigger, self.reload_parameters_service)

        # Timer for checking config file changes
        self.config_check_timer = rospy.Timer(rospy.Duration(5.0), self.check_config_file_changes)  # Check every 5 seconds

        # Status publishing
        self.status_timer = rospy.Timer(rospy.Duration(20.0), self.publish_status)  # Publish status every 20 seconds

        rospy.loginfo("Parameter reload service available at: %s", rospy.resolve_name('~reload_parameters'))
        rospy.loginfo("Monitoring config file for changes: %s", self.config_file_path)

    def load_yaml(self, file_path):
        """
        Load YAML data from a file.
        """
        try:
            with open(file_path, 'r') as file:
                return yaml.safe_load(file)
        except Exception as e:
            rospy.logerr("Failed to load YAML file: %s", str(e))
            return {}

    def odom_callback(self, msg):
        """
        Callback function to update the current position and orientation from odometry data.
        """
        self.current_x = msg.pose.pose.position.x
        self.current_y = msg.pose.pose.position.y

        # Convert quaternion to Euler angles (yaw)
        quaternion = (
            msg.pose.pose.orientation.x,
            msg.pose.pose.orientation.y,
            msg.pose.pose.orientation.z,
            msg.pose.pose.orientation.w
        )
        euler = tf.transformations.euler_from_quaternion(quaternion)
        self.current_yaw = euler[2]  # Yaw angle in radians

        #rospy.loginfo(f"Current X: {self.current_x}, Y: {self.current_y}, Yaw: {self.current_yaw}")

    def mode_callback(self, msg):
        """
        Callback function to handle mode changes from the web interface.
        """
        mode = msg.data.lower()
        previous_mode = self.autonomous_mode

        if mode == 'autonomous':
            self.autonomous_mode = True
            rospy.loginfo("Visual servoing enabled - Autonomous mode")

            # If switching from manual to autonomous, reset state and distance
            if not previous_mode:  # Was in manual mode
                rospy.loginfo("Switching from manual to autonomous - resetting state and distance")
                self.state = "following"  # Always start in following mode
                self.reset_distance_tracking()  # Reset distance tracking
                self.no_green_frames = 0  # Reset green frame counter
                rospy.loginfo("State reset to 'following', distance tracking reset")

        elif mode == 'manual':
            self.autonomous_mode = False
            rospy.loginfo("Visual servoing disabled - Manual mode")

            # Stop the robot when switching to manual mode
            twist = Twist()
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            self.cmd_pub.publish(twist)

            # Reset distance tracking when switching to manual mode
            if previous_mode:  # Was in autonomous mode
                rospy.loginfo("Switching to manual mode - resetting distance tracking")
                self.reset_distance_tracking()

        # Publish status
        status_msg = Bool()
        status_msg.data = self.autonomous_mode
        self.status_pub.publish(status_msg)

    def reset_distance_tracking(self):
        """
        Reset distance tracking by updating initial position to current position.
        This is called when switching modes to reset the row distance measurement.
        """
        self.initial_x = self.current_x
        self.initial_y = self.current_y
        self.initial_yaw = self.current_yaw
        rospy.loginfo(f"Distance tracking reset - New initial position: X={self.initial_x:.2f}, Y={self.initial_y:.2f}, Yaw={np.degrees(self.initial_yaw):.2f}°")

    def publish_cmd_vel(self, twist):
        """
        Publishes cmd_vel only if in autonomous mode.
        """
        if self.autonomous_mode:
            self.cmd_pub.publish(twist)
        else:
            # In manual mode, don't publish anything to avoid conflicts
            pass

    def execute_turning_maneuver(self):
        twist = Twist()

        if self.state == "exiting":
            rospy.loginfo("Exiting row...")

            # SET INITIAL POSITION AND ORIENTATION HERE
            self.initial_x = self.current_x
            self.initial_y = self.current_y
            self.initial_yaw = self.current_yaw
            rospy.loginfo(f"Setting initial pose at start of exiting: X={self.initial_x:.2f}, Y={self.initial_y:.2f}, Yaw={np.degrees(self.initial_yaw):.2f}")

            # Move forward by distance A
            target_distance = self.A
            if self.move_straight(target_distance, self.linear_speed):
                self.state = "rotating1"
                rospy.loginfo("Exiting row complete. Rotating...")

        elif self.state == "rotating1":
            # Rotate by angle theta
            target_angle = self.theta * self.row_direction  # <--- Modified
            if self.rotate(target_angle, self.angular_speed):
                self.state = "diagonal1"
                rospy.loginfo("Rotating 1 complete. Moving Diagonally...")

        elif self.state == "diagonal1":
            # Move diagonally by distance R
            target_distance = self.R
            if self.move_straight(target_distance, self.linear_speed):
                self.state = "rotating2"
                rospy.loginfo("Diagonal 1 complete. Rotating...")

        elif self.state == "rotating2":
            # Rotate by angle (180 - 2*theta)
            target_angle = (np.pi - 2 * self.theta) * self.row_direction  # <--- Modified
            if self.rotate(target_angle, self.angular_speed):
                self.state = "diagonal2"
                rospy.loginfo("Rotating 2 complete. Moving Diagonally...")

        elif self.state == "diagonal2":
            # Move diagonally by distance R
            target_distance = self.R
            if self.move_straight(target_distance, self.linear_speed):
                self.state = "rotating3"
                rospy.loginfo("Diagonal 2 complete. Rotating....")

        elif self.state == "rotating3":
            # Rotate by angle theta
            offset = -0.5 if self.row_direction > 0 else 0.5
            target_angle = (self.theta * self.row_direction + offset)  # <--- Modified
            if self.rotate(target_angle, self.angular_speed):
                self.state = "following"
                rospy.loginfo("Rotating 3 complete. Entering row...")

                self.row_count += 1
                self.row_direction *= -1
                rospy.loginfo(f"Moving to row: {self.row_count}, Direction: {'Forward' if self.row_direction == 1 else 'Backward'}")

                # KEEP THIS HERE - Update initial pose AFTER entering the new row
                self.initial_x = self.current_x
                self.initial_y = self.current_y
                self.initial_yaw = self.current_yaw
                rospy.loginfo(f"Setting initial pose at end of entering (new row): X={self.initial_x:.2f}, Y={self.initial_y:.2f}, Yaw={np.degrees(self.initial_yaw):.2f}")
            else:
                # Should not happen
                rospy.logerr("Invalid state!")
                twist = Twist()
                twist.linear.x = 0.0
                twist.angular.z = 0.0
                self.publish_cmd_vel(twist)
                return



        #this state is not executed
        elif self.state == "entering":
            # Move forward by distance A
            target_distance = self.A
            if self.move_straight(target_distance, self.linear_speed):
                rospy.loginfo("Entering row complete. Switching to following...")
                self.state = "following"

                # NEW: Update row count and direction
                self.row_count += 1
                self.row_direction *= -1
                rospy.loginfo(f"Moving to row: {self.row_count}, Direction: {'Forward' if self.row_direction == 1 else 'Backward'}")

                # KEEP THIS HERE - Update initial pose AFTER entering the new row
                self.initial_x = self.current_x
                self.initial_y = self.current_y
                self.initial_yaw = self.current_yaw
                rospy.loginfo(f"Setting initial pose at end of entering (new row): X={self.initial_x:.2f}, Y={self.initial_y:.2f}, Yaw={np.degrees(self.initial_yaw):.2f}")

        else:
            # Should not happen
            rospy.logerr("Invalid state!")
            twist = Twist()
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            self.publish_cmd_vel(twist)
            return


    def move_straight(self, distance, speed):
        """
        Moves the robot straight for a given distance.  Returns True if the movement is complete.
        """
        twist = Twist()
        start_time = rospy.Time.now()
        if self.use_feedback:
            start_x = self.current_x
            start_y = self.current_y
            traveled_distance = 0.0
            rospy.loginfo(f"Moving straight for {distance} meters (using feedback)")

            while traveled_distance < distance:
                twist.linear.x = speed
                self.publish_cmd_vel(twist)
                traveled_distance = np.sqrt((self.current_x - start_x)**2 + (self.current_y - start_y)**2)
                self.rate.sleep()
        else:
            # Time-based movement
            move_duration = distance / speed  # Calculate the time needed to move
            rospy.loginfo(f"Moving straight for {distance} meters (time-based, duration: {move_duration})")
            twist.linear.x = speed
            self.publish_cmd_vel(twist)
            rospy.sleep(move_duration)  # Move for the calculated duration

        # Stop the robot
        twist.linear.x = 0.0
        self.publish_cmd_vel(twist)
        rospy.loginfo("Move straight completed.")
        return True

    def rotate(self, angle, angular_speed):
        """
        Rotates the robot by a given angle (in radians) using incremental feedback.
        Returns True when the desired rotation is reached.
        """
        # Record the starting yaw
        start_yaw = self.current_yaw
        prev_yaw = start_yaw
        rotated = 0.0
        direction = np.sign(angle)
        target = abs(angle)

        rospy.loginfo(f"Start Yaw: {np.degrees(start_yaw):.2f}°, Rotating {np.degrees(angle):.2f}° at {angular_speed} rad/s")

        twist = Twist()
        rate = self.rate   # Make sure self.rate is defined, e.g., rospy.Rate(10)

        while abs(rotated) < target:
            #Rotate the robot
            twist.angular.z = direction * angular_speed
            self.publish_cmd_vel(twist)
            rate.sleep()

            # Calculate the actual change in yaw since the last loop
            delta = np.arctan2(
                np.sin(self.current_yaw - prev_yaw),
                np.cos(self.current_yaw - prev_yaw)
            )
            rotated += delta
            prev_yaw = self.current_yaw

            rospy.loginfo(f"Rotated so far: {np.degrees(rotated):.2f}° / {np.degrees(target):.2f}°")

        # Stop the rotation
        twist.angular.z = 0.0
        self.publish_cmd_vel(twist)
        rospy.loginfo("Rotation completed.")
        return True


    def image_callback(self, msg):
        try:
            # Skip all visual servoing processing if in manual mode
            if not self.autonomous_mode:
                # In manual mode, only show frames if tuning_mode is enabled for debugging
                if self.tuning_mode and self.show_frames:
                    try:
                        if msg.encoding == "rgb8":
                            cv_image = self.bridge.imgmsg_to_cv2(msg, "rgb8")
                            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)
                        else:
                            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")

                        # Show manual mode indicator
                        cv2.putText(cv_image, "MANUAL MODE - Visual Servoing Disabled",
                                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                        cv2.imshow("Manual Mode", cv_image)
                        cv2.waitKey(1)
                    except Exception as e:
                        rospy.logwarn(f"Error displaying manual mode image: {e}")
                return  # Exit early, no visual servoing processing

            # Add more debug information
            rospy.loginfo(f"Received image message on topic: {self.image_topic}")
            rospy.loginfo(f"Image encoding: {msg.encoding}")

            # Try to convert the image with the correct encoding
            try:
                if msg.encoding == "rgb8":
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "rgb8")
                    cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)
                else:
                    cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            except Exception as e:
                rospy.logerr(f"Error converting image: {e}")
                # Fallback: try with desired encoding
                cv_image = self.bridge.imgmsg_to_cv2(msg)
                if len(cv_image.shape) == 2:  # If grayscale
                    cv_image = cv2.cvtColor(cv_image, cv2.COLOR_GRAY2BGR)
                elif cv_image.shape[2] == 3:  # If RGB
                    cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)

            # Check if image is valid
            if cv_image is None or cv_image.size == 0:
                rospy.logerr("Received empty image!")
                return

            # Log image stats to help debug
            rospy.loginfo(f"Image shape: {cv_image.shape}, dtype: {cv_image.dtype}")
            rospy.loginfo(f"Image min: {cv_image.min()}, max: {cv_image.max()}, mean: {cv_image.mean()}")

            desired_width, desired_height = 640, 480  # Resize if needed
            cv_image = cv2.resize(cv_image, (desired_width, desired_height), interpolation=cv2.INTER_LINEAR)

            # 1. Filter points using the tracker's current window position
            all_points = self.detect_potential_points(cv_image) # Detect points in the whole image

            num_green_pixels = len(all_points)
            rospy.loginfo(f"Number of green pixels: {num_green_pixels}")

            # Check for end of row based on visual input *first*
            if self.state == "following":

                if not self.following_only and num_green_pixels < 50:  # Adjust threshold
                    self.no_green_frames += 1
                    if self.no_green_frames >= self.no_green_threshold:
                        rospy.loginfo("No crop row detected for multiple frames! Initiating turning maneuver.")
                        self.state = "exiting"  # Transition to turning state
                        self.initial_yaw = self.current_yaw #save the inital orientation
                        self.initial_x = self.current_x #save the initial x position
                        self.initial_y = self.current_y #save the initial y position
                        self.no_green_frames = 0 # Reset the counter
                        return  # Exit callback to start the turning process
                else:
                    self.no_green_frames = 0 # Reset counter if green pixels are detected

                distance_traveled = np.sqrt((self.current_x - self.initial_x)**2 + (self.current_y - self.initial_y)**2)
                rospy.loginfo(f"Distance traveled: {distance_traveled}")

                # Wrong distance check (only if use_odometry is true)
                if self.use_odometry:
                    if distance_traveled > self.wrong_distance_threshold:
                        rospy.logerr("ERROR: Wrong distance traveled! Stopping the robot.")
                        twist = Twist()
                        twist.linear.x = 0.0
                        twist.angular.z = 0.0
                        self.publish_cmd_vel(twist)
                        return  # Stop further processing


            # Execute turning maneuver if not following and following_only is not enabled
            elif self.state != "following" and not self.following_only:
                self.execute_turning_maneuver()
                return # Skip the rest of the image processing
            # If following_only is enabled and we're not in following state, force back to following
            elif self.following_only and self.state != "following":
                rospy.loginfo("Following-only mode enabled. Forcing state back to 'following'.")
                self.state = "following"

            self.tracker.filter_points(all_points)
            points_in_neighbourhood = self.tracker.points_in_neighbourhood

            # 2. Fit line to points in the neighbourhood
            line_params = self.fit_and_draw_line(cv_image, points_in_neighbourhood)  # Use the filtered points

            if line_params:
                self.line_detected = True
            else:
                self.line_detected = False

            self.follow_line(line_params)

            # 3. Update the tracker's window position for the next frame
            self.tracker.update_position_for_next_frame(all_points)  # Use all points from this frame

            # 4. Draw the neighbourhood on the image (for visualization)
            if self.show_frames:
                display_frame = cv_image.copy()
                self.tracker.draw(display_frame)
                for pt in points_in_neighbourhood:
                    cv2.circle(display_frame, (int(pt[0]), int(pt[1])), 3, (0, 255, 255), -1)  # Yellow dots

                # Show the camera screen if show_frames is enabled
                cv2.imshow("Tracking Window", display_frame)
                cv2.waitKey(1)

            self.rate.sleep()

        except Exception as e:
            rospy.logerr(f"Error in image_callback: {e}")

    def detect_potential_points(self, image):
        """
        Detects potential crop row points in the entire image using HSV masking and contour extraction.

        Args:
            image (np.ndarray): The input image.

        Returns:
            np.ndarray: An (N, 2) NumPy array of detected points (x, y).
        """
        # Display the original image for debugging


        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # Display the HSV image for debugging
        if self.tuning_mode:
            hsv_display = hsv_image.copy()
            cv2.imshow("HSV Image", hsv_display)
            cv2.waitKey(1)

        mask = cv2.inRange(hsv_image, (self.h_min, self.s_min, self.v_min),
                                     (self.h_max, self.s_max, self.v_max))

        # Display the mask for debugging
        if self.tuning_mode:
            cv2.imshow("HSV Mask", mask)
            cv2.waitKey(1)

        # Enhanced visualization for tuning mode
        if self.tuning_mode:
            # Create a combined HSV visualization
            h, s, v = cv2.split(hsv_image)
            h_display = cv2.normalize(h, None, 0, 255, cv2.NORM_MINMAX)
            s_display = cv2.normalize(s, None, 0, 255, cv2.NORM_MINMAX)
            v_display = cv2.normalize(v, None, 0, 255, cv2.NORM_MINMAX)

            # Convert to BGR for display
            h_display = cv2.cvtColor(h_display, cv2.COLOR_GRAY2BGR)
            s_display = cv2.cvtColor(s_display, cv2.COLOR_GRAY2BGR)
            v_display = cv2.cvtColor(v_display, cv2.COLOR_GRAY2BGR)

            # Add text labels
            cv2.putText(h_display, "H", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(s_display, "S", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(v_display, "V", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            # Combine the channels horizontally
            hsv_channels = np.hstack((h_display, s_display, v_display))
            cv2.imshow("HSV Channels", hsv_channels)

            # Display current HSV range values
            hsv_info = np.zeros((100, 600, 3), dtype=np.uint8)
            cv2.putText(hsv_info, f"H: {self.h_min}-{self.h_max}", (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(hsv_info, f"S: {self.s_min}-{self.s_max}", (220, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(hsv_info, f"V: {self.v_min}-{self.v_max}", (420, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(hsv_info, "Adjust values in kinect_row_crop_config.yaml", (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 200, 255), 2)
            cv2.imshow("HSV Range Values", hsv_info)
            cv2.waitKey(1)

        # Check if the mask is empty (all black pixels)
        if cv2.countNonZero(mask) == 0:
            # If no green pixels are found, return an empty array immediately
            return np.empty((0, 2), dtype=np.float32)

        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)

        contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        filtered_contours = [cnt for cnt in contours if 150 < cv2.contourArea(cnt) < 100000]

        # Enhanced contour visualization for tuning mode
        if self.tuning_mode:
            # Create a copy of the original image for contour display
            contour_display = image.copy()

            # Draw all contours
            cv2.drawContours(contour_display, contours, -1, (0, 0, 255), 1)  # All contours in red

            # Draw filtered contours
            cv2.drawContours(contour_display, filtered_contours, -1, (0, 255, 0), 2)  # Filtered contours in green

            # Add contour count information
            cv2.putText(contour_display, f"All contours: {len(contours)}", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            cv2.putText(contour_display, f"Filtered contours: {len(filtered_contours)}", (10, 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            cv2.imshow("Contours", contour_display)
            cv2.waitKey(1)

        points = []
        for contour in filtered_contours:
            for point in contour:
                x, y = point[0]
                points.append([float(x), float(y)])  # Convert to float

        return np.array(points, dtype=np.float32) if points else np.empty((0, 2), dtype=np.float32)

    def fit_and_draw_line(self, image, points):
        if len(points) >= 2:
            points = np.array(points, dtype=np.float32)
            [vx, vy, x, y] = cv2.fitLine(points, cv2.DIST_L2, 0, 0.01, 0.01)

            if abs(vx) < 1e-6:
                rospy.logwarn("Detected line is nearly vertical! Skipping.")
                return None

            left_y = int((-x * vy / vx) + y)
            right_y = int(((image.shape[1] - x) * vy / vx) + y)
            cv2.line(image, (0, left_y), (image.shape[1], right_y), (0, 0, 255), 2)
            return (vx, vy, x, y)
        elif len(points) > 0:
            rospy.logwarn("Not enough points to fit a line! Need at least 2. Points found: " + str(len(points)))
            return None
        else:
            rospy.logwarn("No points to fit a line!")
            return None

    def follow_line(self, line_params):
        twist = Twist()

        if self.line_detected and line_params:
            vx, vy, x, y = line_params
            image_center = 320  # Assuming 640px width
            error = (image_center - x).item()  # Convert NumPy array to scalar

            twist.linear.x = self.linear_speed
            twist.angular.z = -self.kp * error
            rospy.loginfo(f"Following line: Error={error:.2f}, Angular.z={twist.angular.z:.2f}")
        else:
            twist.linear.x = 0.0
            twist.angular.z = 0.0
            rospy.logwarn("Stopping: No line detected!")

        self.publish_cmd_vel(twist)

    def get_config_file_modified_time(self):
        """Get the last modified time of the config file"""
        try:
            if os.path.exists(self.config_file_path):
                return os.path.getmtime(self.config_file_path)
            else:
                rospy.logwarn("Config file does not exist: %s", self.config_file_path)
                return 0
        except Exception as e:
            rospy.logerr("Error getting config file modified time: %s", str(e))
            return 0

    def publish_status(self, event=None):
        """Publish parameter reload status message"""
        try:
            status_msg = ParameterReloadStatus()
            status_msg.header = Header()
            status_msg.header.stamp = rospy.Time.now()
            status_msg.header.frame_id = "row_crop_follower"

            status_msg.reload_success = True  # Assume success if we can publish
            status_msg.config_file_name = os.path.basename(self.config_file_path)
            status_msg.config_file_path = self.config_file_path
            status_msg.last_reload_time = self.last_reload_time

            # Current parameter values
            status_msg.row_length = self.row_length
            status_msg.row_spacing = self.row_spacing
            status_msg.field_direction = self.field_direction
            status_msg.linear_speed = self.linear_speed
            status_msg.angular_speed = self.angular_speed

            status_msg.reload_message = f"Parameters loaded successfully from {status_msg.config_file_name}"

            status_msg.key_parameters = [
                f"row_length: {self.row_length}",
                f"row_spacing: {self.row_spacing}",
                f"field_direction: {self.field_direction}",
                f"linear_speed: {self.linear_speed}",
                f"angular_speed: {self.angular_speed}",
                f"A: {self.A}",
                f"theta_degrees: {np.degrees(self.theta)}",
                f"kp: {self.kp}",
                f"wrong_distance_threshold: {self.wrong_distance_threshold}",
                f"neighbourhood_width: {self.neighbourhood_width}",
                f"neighbourhood_height: {self.neighbourhood_height}"
            ]

            self.reload_status_pub.publish(status_msg)

        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

    def check_config_file_changes(self, event=None):
        """Check if the config file has been modified and reload if necessary"""
        try:
            current_modified_time = self.get_config_file_modified_time()
            if current_modified_time > self.last_config_modified_time:
                rospy.loginfo("Detected change in config file, reloading parameters...")
                if self.reload_parameters():
                    self.last_config_modified_time = current_modified_time
                    self.last_reload_time = rospy.Time.now()
                    rospy.loginfo("Parameters reloaded successfully due to file change")
                    # Publish status immediately after successful reload
                    self.publish_status()
                else:
                    rospy.logerr("Failed to reload parameters after file change")
        except Exception as e:
            rospy.logerr("Error checking config file changes: %s", str(e))

    def reload_parameters_service(self, req):
        """Service callback to reload parameters"""
        try:
            success = self.reload_parameters()
            if success:
                self.last_config_modified_time = self.get_config_file_modified_time()
                self.last_reload_time = rospy.Time.now()
                # Publish status immediately after successful reload
                self.publish_status()
                return TriggerResponse(success=True, message="Parameters reloaded successfully")
            else:
                return TriggerResponse(success=False, message="Failed to reload parameters")
        except Exception as e:
            rospy.logerr("Error in reload parameters service: %s", str(e))
            return TriggerResponse(success=False, message=f"Error reloading parameters: {str(e)}")

    def reload_parameters(self):
        """Reload parameters from the config file"""
        try:
            # Check if config file parameter changed
            new_config_file_name = rospy.get_param('~config_file', 'row_crop_config.yaml')
            rospack = rospkg.RosPack()
            package_path = rospack.get_path('visual_servoing')
            new_config_file_path = package_path + '/config/' + new_config_file_name

            if new_config_file_path != self.config_file_path:
                rospy.loginfo("Config file parameter changed from %s to %s", self.config_file_path, new_config_file_path)
                self.config_file_path = new_config_file_path

            # Load new parameters from YAML file
            rospy.loginfo("Reloading parameters from %s", self.config_file_path)
            yaml_params = self.load_yaml(self.config_file_path)
            if not yaml_params:
                rospy.logerr("Failed to load YAML parameters")
                return False

            # Update ROS parameter server
            rospy.set_param('row_crop_follower', yaml_params)
            rospy.loginfo("Updated parameter server with new YAML parameters")

            # Reload all parameters
            old_values = {}

            # Field parameters
            old_values['row_length'] = self.row_length
            old_values['row_spacing'] = self.row_spacing
            old_values['field_direction'] = self.field_direction

            self.row_length = rospy.get_param('row_crop_follower/row_length')
            self.row_spacing = rospy.get_param('row_crop_follower/row_spacing')
            self.field_direction = rospy.get_param('row_crop_follower/field_direction')
            self.end_of_row_threshold = rospy.get_param('row_crop_follower/end_of_row_threshold')
            self.wrong_distance_threshold = rospy.get_param('row_crop_follower/wrong_distance_threshold')
            self.use_odometry = rospy.get_param('row_crop_follower/use_odometry')
            self.use_feedback = rospy.get_param('row_crop_follower/use_feedback')
            self.following_only = rospy.get_param('row_crop_follower/following_only', False)
            self.show_frames = rospy.get_param('row_crop_follower/show_frames', True)
            self.tuning_mode = rospy.get_param('row_crop_follower/tuning_mode', False)

            # Neighbourhood tracker parameters
            old_values['neighbourhood_width'] = self.neighbourhood_width
            old_values['neighbourhood_height'] = self.neighbourhood_height
            old_values['initial_x'] = self.initial_x
            old_values['initial_y'] = self.initial_y

            self.neighbourhood_width = rospy.get_param('row_crop_follower/neighbourhood_width')
            self.neighbourhood_height = rospy.get_param('row_crop_follower/neighbourhood_height')
            self.initial_x = rospy.get_param('row_crop_follower/initial_x')
            self.initial_y = rospy.get_param('row_crop_follower/initial_y')

            # HSV parameters
            self.h_min = rospy.get_param('row_crop_follower/h_min')
            self.h_max = rospy.get_param('row_crop_follower/h_max')
            self.s_min = rospy.get_param('row_crop_follower/s_min')
            self.s_max = rospy.get_param('row_crop_follower/s_max')
            self.v_min = rospy.get_param('row_crop_follower/v_min')
            self.v_max = rospy.get_param('row_crop_follower/v_max')
            self.point_spacing = rospy.get_param('row_crop_follower/point_spacing')
            self.kp = rospy.get_param('row_crop_follower/kp')

            # Movement parameters
            old_values['linear_speed'] = self.linear_speed
            old_values['angular_speed'] = self.angular_speed
            old_values['A'] = self.A
            old_values['theta_degrees'] = np.degrees(self.theta)
            old_values['kp'] = self.kp
            old_values['wrong_distance_threshold'] = self.wrong_distance_threshold

            self.A = rospy.get_param('row_crop_follower/A')
            theta_degrees = rospy.get_param('row_crop_follower/theta_degrees')
            self.theta = np.radians(theta_degrees)
            self.linear_speed = rospy.get_param('row_crop_follower/linear_speed')
            self.angular_speed = rospy.get_param('row_crop_follower/angular_speed')

            # Recalculate derived parameters
            self.R = (self.row_spacing * np.sin(180 - (2 * self.theta)) / np.sin(self.theta) + 0.25)

            # Update neighbourhood tracker if dimensions changed
            if (old_values.get('neighbourhood_width') != self.neighbourhood_width or
                old_values.get('neighbourhood_height') != self.neighbourhood_height or
                old_values.get('initial_x') != self.initial_x or
                old_values.get('initial_y') != self.initial_y):

                rospy.loginfo("Neighbourhood tracker parameters changed, updating tracker")
                self.tracker = NeighbourhoodTracker(640, 480, self.initial_x, self.initial_y,
                                                  self.neighbourhood_width, self.neighbourhood_height)

            # Log significant parameter changes
            for param, old_val in old_values.items():
                new_val = getattr(self, param)
                if old_val != new_val:
                    rospy.loginfo("Parameter %s changed: %s -> %s", param, old_val, new_val)

            rospy.loginfo("Successfully reloaded all parameters")
            return True

        except Exception as e:
            rospy.logerr("Error reloading parameters: %s", str(e))
            return False




if __name__ == '__main__':
    try:
        follower = RowCropFollower()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        cv2.destroyAllWindows()