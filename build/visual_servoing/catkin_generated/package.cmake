set(_CATKIN_CURRENT_PACKAGE "visual_servoing")
set(visual_servoing_VERSION "0.0.0")
set(visual_servoing_MAINTAINER "abdulaziz <<EMAIL>>")
set(visual_servoing_PACKAGE_FORMAT "2")
set(visual_servoing_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf" "cv_bridge" "message_generation")
set(visual_servoing_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(visual_servoing_BUILDTOOL_DEPENDS "catkin")
set(visual_servoing_BUILDTOOL_EXPORT_DEPENDS )
set(visual_servoing_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf" "cv_bridge" "rosparam" "rospkg" "message_runtime")
set(visual_servoing_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf" "cv_bridge" "rosparam" "rospkg" "message_runtime")
set(visual_servoing_TEST_DEPENDS )
set(visual_servoing_DOC_DEPENDS )
set(visual_servoing_URL_WEBSITE "")
set(visual_servoing_URL_BUGTRACKER "")
set(visual_servoing_URL_REPOSITORY "")
set(visual_servoing_DEPRECATED "")