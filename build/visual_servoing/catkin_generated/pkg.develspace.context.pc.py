# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/weednix_ws/src/build/devel/include".split(';') if "/home/<USER>/weednix_ws/src/build/devel/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rospy;std_msgs;sensor_msgs;geometry_msgs;nav_msgs;tf;cv_bridge;message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "visual_servoing"
PROJECT_SPACE_DIR = "/home/<USER>/weednix_ws/src/build/devel"
PROJECT_VERSION = "0.0.0"
