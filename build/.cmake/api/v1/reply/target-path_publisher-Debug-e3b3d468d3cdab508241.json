{"artifacts": [{"path": "devel/lib/visual_servoing/path_publisher"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "add_definitions", "include", "find_package", "configure_shared_library_build_settings", "include_directories", "gencpp_append_include_dirs", "_generate_msg_cpp", "generate_messages"], "files": ["visual_servoing/CMakeLists.txt", "/opt/ros/noetic/share/rosconsole/cmake/rosconsole-extras.cmake", "/opt/ros/noetic/share/rosconsole/cmake/rosconsoleConfig.cmake", "/opt/ros/noetic/share/roscpp/cmake/roscppConfig.cmake", "/opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake", "/opt/ros/noetic/share/catkin/cmake/tools/libraries.cmake", "/opt/ros/noetic/share/catkin/cmake/all.cmake", "CMakeLists.txt", "/opt/ros/noetic/share/gencpp/cmake/gencpp-extras.cmake", "build/visual_servoing/cmake/visual_servoing-genmsg.cmake", "/opt/ros/noetic/share/genmsg/cmake/genmsg-extras.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 147, "parent": 0}, {"command": 3, "file": 0, "line": 10, "parent": 0}, {"file": 4, "parent": 2}, {"command": 3, "file": 4, "line": 76, "parent": 3}, {"file": 3, "parent": 4}, {"command": 3, "file": 3, "line": 197, "parent": 5}, {"file": 2, "parent": 6}, {"command": 2, "file": 2, "line": 222, "parent": 7}, {"file": 1, "parent": 8}, {"command": 1, "file": 1, "line": 12, "parent": 9}, {"file": 7}, {"command": 3, "file": 7, "line": 58, "parent": 11}, {"file": 4, "parent": 12}, {"command": 2, "file": 4, "line": 20, "parent": 13}, {"file": 6, "parent": 14}, {"command": 4, "file": 6, "line": 174, "parent": 15}, {"command": 1, "file": 5, "line": 18, "parent": 16}, {"command": 1, "file": 1, "line": 8, "parent": 9}, {"command": 8, "file": 0, "line": 79, "parent": 0}, {"command": 2, "file": 10, "line": 307, "parent": 19}, {"file": 9, "parent": 20}, {"command": 7, "file": 9, "line": 31, "parent": 21}, {"command": 6, "file": 8, "line": 46, "parent": 22}, {"command": 5, "file": 8, "line": 63, "parent": 23}, {"command": 5, "file": 0, "line": 126, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g  "}], "defines": [{"backtrace": 10, "define": "ROSCONSOLE_BACKEND_LOG4CXX"}, {"backtrace": 17, "define": "ROS_BUILD_SHARED_LIBS=1"}, {"backtrace": 18, "define": "ROS_PACKAGE_NAME=\"visual_servoing\""}], "includes": [{"backtrace": 24, "path": "/home/<USER>/weednix_ws/src/build/devel/include"}, {"backtrace": 25, "path": "/opt/ros/noetic/include"}, {"backtrace": 25, "path": "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"}, {"backtrace": 25, "path": "/usr/include/opencv4"}], "language": "CXX", "sourceIndexes": [0]}], "id": "path_publisher::@1e09fb0d76ac329dfbf9", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/ros/noetic/lib", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libtf.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libtf2_ros.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libactionlib.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libroscpp.so", "role": "libraries"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libxmlrpcpp.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libtf2.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libcv_bridge.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole_log4cxx.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librosconsole_backend_interface.so", "role": "libraries"}, {"fragment": "-llog4cxx", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libroscpp_serialization.so", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/librostime.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0", "role": "libraries"}, {"fragment": "/opt/ros/noetic/lib/libcpp_common.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4", "role": "libraries"}], "language": "CXX"}, "name": "path_publisher", "nameOnDisk": "path_publisher", "paths": {"build": "visual_servoing", "source": "visual_servoing"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "visual_servoing/src/path_publisher.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}