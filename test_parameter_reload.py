#!/usr/bin/env python3

"""
Test script for parameter reloading functionality.
This script tests both the geofencing node and row crop follower parameter reload services.
"""

import rospy
import sys
from std_srvs.srv import Trigger
from weednix_sensors.msg import ReloadStatus
from visual_servoing.msg import ParameterReloadStatus

def test_geofencing_reload():
    """Test the geofencing node boundary reload service"""
    print("Testing geofencing node boundary reload...")
    
    try:
        # Wait for the service to be available
        rospy.wait_for_service('/geofencing_node/reload_boundary', timeout=5.0)
        
        # Create service proxy
        reload_boundary = rospy.ServiceProxy('/geofencing_node/reload_boundary', Trigger)
        
        # Call the service
        response = reload_boundary()
        
        if response.success:
            print(f"✅ Geofencing reload successful: {response.message}")
            return True
        else:
            print(f"❌ Geofencing reload failed: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ Geofencing service call failed: {e}")
        return False
    except rospy.ROSException as e:
        print(f"❌ Geofencing service not available: {e}")
        return False

def test_row_crop_follower_reload():
    """Test the row crop follower parameter reload service"""
    print("Testing row crop follower parameter reload...")
    
    try:
        # Wait for the service to be available
        rospy.wait_for_service('/row_crop_follower/reload_parameters', timeout=5.0)
        
        # Create service proxy
        reload_params = rospy.ServiceProxy('/row_crop_follower/reload_parameters', Trigger)
        
        # Call the service
        response = reload_params()
        
        if response.success:
            print(f"✅ Row crop follower reload successful: {response.message}")
            return True
        else:
            print(f"❌ Row crop follower reload failed: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ Row crop follower service call failed: {e}")
        return False
    except rospy.ROSException as e:
        print(f"❌ Row crop follower service not available: {e}")
        return False

def test_status_topics():
    """Test status topic publishing"""
    print("Testing status topic publishing...")

    geofencing_status_received = False
    row_crop_status_received = False

    def geofencing_status_callback(msg):
        nonlocal geofencing_status_received
        geofencing_status_received = True
        print(f"✅ Geofencing status received: {msg.reload_message}")
        print(f"   Config file: {msg.config_file_name}")
        print(f"   Boundary points: {msg.boundary_points_count}")
        print(f"   Last reload: {msg.last_reload_time}")

    def row_crop_status_callback(msg):
        nonlocal row_crop_status_received
        row_crop_status_received = True
        print(f"✅ Row crop status received: {msg.reload_message}")
        print(f"   Config file: {msg.config_file_name}")
        print(f"   Row length: {msg.row_length}m")
        print(f"   Row spacing: {msg.row_spacing}m")
        print(f"   Last reload: {msg.last_reload_time}")

    # Subscribe to status topics
    geofencing_sub = rospy.Subscriber('/geofencing_node/reload_status', ReloadStatus, geofencing_status_callback)
    row_crop_sub = rospy.Subscriber('/row_crop_follower/reload_status', ParameterReloadStatus, row_crop_status_callback)

    print("Waiting for status messages (10 seconds)...")

    # Wait for messages
    timeout = rospy.Time.now() + rospy.Duration(10.0)
    while rospy.Time.now() < timeout and not rospy.is_shutdown():
        if geofencing_status_received and row_crop_status_received:
            break
        rospy.sleep(0.1)

    # Cleanup
    geofencing_sub.unregister()
    row_crop_sub.unregister()

    success = geofencing_status_received and row_crop_status_received
    if not success:
        if not geofencing_status_received:
            print("❌ No geofencing status received")
        if not row_crop_status_received:
            print("❌ No row crop status received")

    return success

def main():
    """Main test function"""
    rospy.init_node('parameter_reload_test', anonymous=True)

    print("🧪 Parameter Reload Test Script")
    print("=" * 50)

    # Test both services
    geofencing_success = test_geofencing_reload()
    print()
    row_crop_success = test_row_crop_follower_reload()
    print()
    status_topics_success = test_status_topics()

    print()
    print("=" * 50)
    print("📊 Test Results:")
    print(f"Geofencing Node Service: {'✅ PASS' if geofencing_success else '❌ FAIL'}")
    print(f"Row Crop Follower Service: {'✅ PASS' if row_crop_success else '❌ FAIL'}")
    print(f"Status Topics: {'✅ PASS' if status_topics_success else '❌ FAIL'}")

    if geofencing_success and row_crop_success and status_topics_success:
        print("🎉 All tests passed! Parameter reloading and status monitoring is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check node status and configuration.")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        print("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test failed with error: {e}")
        sys.exit(1)
