.camera-feed {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 10px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.camera-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.4em;
  font-weight: 600;
}

.camera-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.topic-selector {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 0.9em;
  min-width: 200px;
}

.topic-selector:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 0.9em;
  font-weight: 500;
  color: #666;
}

.camera-container {
  position: relative;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.camera-canvas {
  max-width: 100%;
  max-height: 400px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-message {
  text-align: center;
  color: #d32f2f;
  padding: 20px;
}

.error-message p {
  margin: 0 0 15px 0;
  font-size: 1.1em;
}

.error-message button {
  background: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.3s;
}

.error-message button:hover:not(:disabled) {
  background: #1976D2;
}

.error-message button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading-message {
  text-align: center;
  color: #666;
  padding: 20px;
}

.loading-message p {
  margin: 0;
  font-size: 1.1em;
  animation: fadeInOut 2s infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.disconnected-message {
  text-align: center;
  color: #999;
  padding: 20px;
}

.disconnected-message p {
  margin: 0;
  font-size: 1.1em;
}

.image-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.85em;
  color: #666;
  flex-wrap: wrap;
  gap: 10px;
}

.image-info span {
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .camera-feed {
    padding: 15px;
  }
  
  .camera-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .camera-controls {
    justify-content: space-between;
  }
  
  .topic-selector {
    min-width: auto;
    flex: 1;
  }
  
  .camera-container {
    min-height: 250px;
  }
  
  .image-info {
    flex-direction: column;
    align-items: stretch;
  }
  
  .image-info span {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .camera-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .status-indicator {
    justify-content: center;
  }
}
